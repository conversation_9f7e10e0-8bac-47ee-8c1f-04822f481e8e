"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(admin)/users/page",{

/***/ "(app-pages-browser)/./src/app/(admin)/users/page.tsx":
/*!****************************************!*\
  !*** ./src/app/(admin)/users/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_api_helpers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api-helpers */ \"(app-pages-browser)/./src/lib/api-helpers.ts\");\n/* harmony import */ var _components_users_user_stats_cards__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/users/user-stats-cards */ \"(app-pages-browser)/./src/components/users/user-stats-cards.tsx\");\n/* harmony import */ var _components_users_user_filters__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/users/user-filters */ \"(app-pages-browser)/./src/components/users/user-filters.tsx\");\n/* harmony import */ var _components_users_user_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/users/user-table */ \"(app-pages-browser)/./src/components/users/user-table.tsx\");\n/* harmony import */ var _components_users_user_pagination__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/users/user-pagination */ \"(app-pages-browser)/./src/components/users/user-pagination.tsx\");\n/* harmony import */ var _components_users_user_action_dialogs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/users/user-action-dialogs */ \"(app-pages-browser)/./src/components/users/user-action-dialogs.tsx\");\n/* harmony import */ var _services_user_service__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/services/user.service */ \"(app-pages-browser)/./src/services/user.service.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Import our new components\n\n\n\n\n\n// Import services and types\n\nfunction UsersPage() {\n    _s();\n    // State management\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        total: 0,\n        active: 0,\n        banned: 0,\n        vip: 0,\n        newThisMonth: 0\n    });\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [statsLoading, setStatsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Pagination state\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 20,\n        total: 0,\n        totalPages: 0\n    });\n    // Filter state\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 20\n    });\n    // Dialog state\n    const [banDialog, setBanDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        open: false,\n        user: null\n    });\n    const [vipDialog, setVipDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        open: false,\n        user: null\n    });\n    // Load users data\n    const loadUsers = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"UsersPage.useCallback[loadUsers]\": async ()=>{\n            const result = await (0,_lib_api_helpers__WEBPACK_IMPORTED_MODULE_6__.apiCall)({\n                \"UsersPage.useCallback[loadUsers]\": ()=>_services_user_service__WEBPACK_IMPORTED_MODULE_12__.UserService.getUsers(filters)\n            }[\"UsersPage.useCallback[loadUsers]\"], {\n                loadingKey: 'loadUsers',\n                showErrorToast: true,\n                errorMessage: '加载用户列表失败'\n            });\n            if (result.success && result.data) {\n                var _result_data_pagination, _result_data_pagination1, _result_data_pagination2, _result_data_pagination3;\n                setUsers(result.data.users);\n                setPagination({\n                    page: ((_result_data_pagination = result.data.pagination) === null || _result_data_pagination === void 0 ? void 0 : _result_data_pagination.page) || 1,\n                    limit: ((_result_data_pagination1 = result.data.pagination) === null || _result_data_pagination1 === void 0 ? void 0 : _result_data_pagination1.limit) || 20,\n                    total: ((_result_data_pagination2 = result.data.pagination) === null || _result_data_pagination2 === void 0 ? void 0 : _result_data_pagination2.total) || 0,\n                    totalPages: ((_result_data_pagination3 = result.data.pagination) === null || _result_data_pagination3 === void 0 ? void 0 : _result_data_pagination3.totalPages) || 0\n                });\n            }\n        }\n    }[\"UsersPage.useCallback[loadUsers]\"], [\n        filters\n    ]);\n    // Load user statistics\n    const loadStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"UsersPage.useCallback[loadStats]\": async ()=>{\n            try {\n                setStatsLoading(true);\n                const response = await _services_user_service__WEBPACK_IMPORTED_MODULE_12__.UserService.getUserStats();\n                if (response.success && response.data) {\n                    setStats(response.data);\n                } else {\n                    sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error('加载统计数据失败');\n                }\n            } catch (error) {\n                console.error('Failed to load stats:', error);\n                sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error('加载统计数据失败');\n            } finally{\n                setStatsLoading(false);\n            }\n        }\n    }[\"UsersPage.useCallback[loadStats]\"], []);\n    // Event handlers\n    const handleFiltersChange = (newFilters)=>{\n        setFilters(newFilters);\n    };\n    const handleFiltersReset = ()=>{\n        setFilters({\n            page: 1,\n            limit: 20\n        });\n    };\n    const handlePageChange = (page)=>{\n        setFilters((prev)=>({\n                ...prev,\n                page\n            }));\n    };\n    const handlePageSizeChange = (limit)=>{\n        setFilters((prev)=>({\n                ...prev,\n                limit,\n                page: 1\n            }));\n    };\n    const handleRefresh = ()=>{\n        loadUsers();\n        loadStats();\n    };\n    // User action handlers\n    const handleBanUser = async (reason, expiresAt)=>{\n        if (!banDialog.user) return;\n        try {\n            setActionLoading(true);\n            const response = await _services_user_service__WEBPACK_IMPORTED_MODULE_12__.UserService.banUser(banDialog.user.id, {\n                reason,\n                expiresAt: expiresAt === null || expiresAt === void 0 ? void 0 : expiresAt.toISOString()\n            });\n            if (response.success) {\n                sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success('用户已封禁');\n                setBanDialog({\n                    open: false,\n                    user: null\n                });\n                loadUsers();\n                loadStats();\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(response.error || '封禁用户失败');\n            }\n        } catch (error) {\n            console.error('Ban user error:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error('封禁用户失败');\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleUnbanUser = async (user)=>{\n        try {\n            setActionLoading(true);\n            const response = await _services_user_service__WEBPACK_IMPORTED_MODULE_12__.UserService.unbanUser(user.id);\n            if (response.success) {\n                sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success('用户已解封');\n                loadUsers();\n                loadStats();\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(response.error || '解封用户失败');\n            }\n        } catch (error) {\n            console.error('Unban user error:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error('解封用户失败');\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleSetVip = async (level, expiresAt)=>{\n        if (!vipDialog.user) return;\n        try {\n            setActionLoading(true);\n            const response = await _services_user_service__WEBPACK_IMPORTED_MODULE_12__.UserService.setVipLevel(vipDialog.user.id, {\n                level: level,\n                expiresAt: expiresAt === null || expiresAt === void 0 ? void 0 : expiresAt.toISOString()\n            });\n            if (response.success) {\n                sonner__WEBPACK_IMPORTED_MODULE_5__.toast.success('VIP等级已设置');\n                setVipDialog({\n                    open: false,\n                    user: null\n                });\n                loadUsers();\n                loadStats();\n            } else {\n                sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error(response.error || '设置VIP等级失败');\n            }\n        } catch (error) {\n            console.error('Set VIP error:', error);\n            sonner__WEBPACK_IMPORTED_MODULE_5__.toast.error('设置VIP等级失败');\n        } finally{\n            setActionLoading(false);\n        }\n    };\n    const handleViewUser = (user)=>{\n        // TODO: Implement user detail view\n        sonner__WEBPACK_IMPORTED_MODULE_5__.toast.info('用户详情功能开发中');\n    };\n    const handleEditUser = (user)=>{\n        // TODO: Implement user edit\n        sonner__WEBPACK_IMPORTED_MODULE_5__.toast.info('编辑用户功能开发中');\n    };\n    // Load data on mount and when filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UsersPage.useEffect\": ()=>{\n            loadUsers();\n        }\n    }[\"UsersPage.useEffect\"], [\n        loadUsers\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UsersPage.useEffect\": ()=>{\n            loadStats();\n        }\n    }[\"UsersPage.useEffect\"], [\n        loadStats\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 space-y-6 p-4 md:p-8 pt-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold tracking-tight\",\n                                children: \"用户管理\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"管理uniapp前端注册用户，包括搜索、封号解封、VIP等级管理等功能\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: handleRefresh,\n                                disabled: loading,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 \".concat(loading ? 'animate-spin' : '')\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"刷新\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"添加用户\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                lineNumber: 219,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_user_stats_cards__WEBPACK_IMPORTED_MODULE_7__.UserStatsCards, {\n                stats: stats,\n                loading: statsLoading\n            }, void 0, false, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                lineNumber: 239,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: \"用户列表\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                                lineNumber: 244,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"管理所有注册用户，支持搜索、筛选和批量操作\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                                lineNumber: 245,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                        lineNumber: 243,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_user_filters__WEBPACK_IMPORTED_MODULE_8__.UserFilters, {\n                                filters: filters,\n                                onFiltersChange: handleFiltersChange,\n                                onReset: handleFiltersReset\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_user_table__WEBPACK_IMPORTED_MODULE_9__.UserTable, {\n                                users: users,\n                                loading: loading,\n                                onBanUser: (user)=>setBanDialog({\n                                        open: true,\n                                        user\n                                    }),\n                                onUnbanUser: handleUnbanUser,\n                                onSetVip: (user)=>setVipDialog({\n                                        open: true,\n                                        user\n                                    }),\n                                onViewUser: handleViewUser,\n                                onEditUser: handleEditUser\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, this),\n                            pagination.total > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_user_pagination__WEBPACK_IMPORTED_MODULE_10__.UserPagination, {\n                                currentPage: pagination.page,\n                                totalPages: pagination.totalPages,\n                                pageSize: pagination.limit,\n                                total: pagination.total,\n                                onPageChange: handlePageChange,\n                                onPageSizeChange: handlePageSizeChange\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                        lineNumber: 249,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                lineNumber: 242,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_user_action_dialogs__WEBPACK_IMPORTED_MODULE_11__.BanUserDialog, {\n                open: banDialog.open,\n                onOpenChange: (open)=>setBanDialog({\n                        open,\n                        user: banDialog.user\n                    }),\n                user: banDialog.user,\n                onConfirm: handleBanUser,\n                loading: actionLoading\n            }, void 0, false, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                lineNumber: 283,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_user_action_dialogs__WEBPACK_IMPORTED_MODULE_11__.SetVipDialog, {\n                open: vipDialog.open,\n                onOpenChange: (open)=>setVipDialog({\n                        open,\n                        user: vipDialog.user\n                    }),\n                user: vipDialog.user,\n                onConfirm: handleSetVip,\n                loading: actionLoading\n            }, void 0, false, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                lineNumber: 291,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n_s(UsersPage, \"QvrGs9Y7y95dtUPCcoDVwQEuM7w=\");\n_c = UsersPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_c1 = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.withAuth)(UsersPage));\nvar _c, _c1;\n$RefreshReg$(_c, \"UsersPage\");\n$RefreshReg$(_c1, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(admin)/users/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api-helpers.ts":
/*!********************************!*\
  !*** ./src/lib/api-helpers.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiPresets: () => (/* binding */ ApiPresets),\n/* harmony export */   LoadingManager: () => (/* binding */ LoadingManager),\n/* harmony export */   apiCall: () => (/* binding */ apiCall),\n/* harmony export */   batchApiCall: () => (/* binding */ batchApiCall),\n/* harmony export */   createApiCaller: () => (/* binding */ createApiCaller)\n/* harmony export */ });\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _api_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/**\n * API调用辅助工具\n * 提供统一的API调用方式，自动处理错误和加载状态\n */ \n\nlet globalLoadingState = {};\nconst loadingListeners = [];\n// 加载状态管理器\nconst LoadingManager = {\n    setLoading (key, loading) {\n        globalLoadingState = {\n            ...globalLoadingState,\n            [key]: loading\n        };\n        loadingListeners.forEach((listener)=>listener(globalLoadingState));\n    },\n    isLoading (key) {\n        return globalLoadingState[key] || false;\n    },\n    subscribe (listener) {\n        loadingListeners.push(listener);\n        return ()=>{\n            const index = loadingListeners.indexOf(listener);\n            if (index > -1) {\n                loadingListeners.splice(index, 1);\n            }\n        };\n    },\n    getState () {\n        return {\n            ...globalLoadingState\n        };\n    }\n};\n/**\n * 统一的API调用函数\n * 自动处理加载状态、错误处理、成功提示等\n */ async function apiCall(apiFunction) {\n    let config = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { loadingKey, showSuccessToast = false, successMessage, showErrorToast = true, errorMessage, redirectOnAuth = true, retryCount = 0, retryDelay = 1000 } = config;\n    // 设置加载状态\n    if (loadingKey) {\n        LoadingManager.setLoading(loadingKey, true);\n    }\n    let lastError;\n    // 重试机制\n    for(let attempt = 0; attempt <= retryCount; attempt++){\n        try {\n            const response = await apiFunction();\n            // 清除加载状态\n            if (loadingKey) {\n                LoadingManager.setLoading(loadingKey, false);\n            }\n            // 检查响应格式\n            if (response && typeof response === 'object') {\n                // 标准API响应格式\n                if ('success' in response) {\n                    if (response.success) {\n                        // 成功响应\n                        if (showSuccessToast && (successMessage || response.message)) {\n                            sonner__WEBPACK_IMPORTED_MODULE_0__.toast.success(successMessage || response.message || '操作成功');\n                        }\n                        return {\n                            success: true,\n                            data: response.data\n                        };\n                    } else {\n                        // 业务错误\n                        const error = response.error || response.message || '操作失败';\n                        if (showErrorToast) {\n                            sonner__WEBPACK_IMPORTED_MODULE_0__.toast.error(errorMessage || error);\n                        }\n                        return {\n                            success: false,\n                            error: errorMessage || error,\n                            code: response.code\n                        };\n                    }\n                } else {\n                    // 直接返回数据的响应\n                    if (showSuccessToast && successMessage) {\n                        sonner__WEBPACK_IMPORTED_MODULE_0__.toast.success(successMessage);\n                    }\n                    return {\n                        success: true,\n                        data: response\n                    };\n                }\n            }\n            // 其他类型的响应\n            if (showSuccessToast && successMessage) {\n                sonner__WEBPACK_IMPORTED_MODULE_0__.toast.success(successMessage);\n            }\n            return {\n                success: true,\n                data: response\n            };\n        } catch (error) {\n            lastError = error;\n            // 如果是最后一次尝试或者是客户端错误，不再重试\n            if (attempt === retryCount || error instanceof _api_client__WEBPACK_IMPORTED_MODULE_1__.ApiClientError && error.statusCode < 500) {\n                break;\n            }\n            // 等待后重试\n            if (attempt < retryCount) {\n                await new Promise((resolve)=>setTimeout(resolve, retryDelay * (attempt + 1)));\n            }\n        }\n    }\n    // 清除加载状态\n    if (loadingKey) {\n        LoadingManager.setLoading(loadingKey, false);\n    }\n    // 处理错误\n    return handleApiError(lastError, {\n        showErrorToast,\n        errorMessage,\n        redirectOnAuth\n    });\n}\n/**\n * 处理API错误\n */ function handleApiError(error, config) {\n    const { showErrorToast = true, errorMessage, redirectOnAuth = true } = config;\n    let finalError = '操作失败，请稍后重试';\n    let code;\n    // ApiClientError\n    if (error instanceof _api_client__WEBPACK_IMPORTED_MODULE_1__.ApiClientError) {\n        finalError = error.message;\n        code = error.code;\n        // 处理认证错误\n        if (error.statusCode === 401 && redirectOnAuth) {\n            if (showErrorToast) {\n                sonner__WEBPACK_IMPORTED_MODULE_0__.toast.error('登录已过期，请重新登录');\n            }\n            // 延迟跳转到登录页\n            setTimeout(()=>{\n                if (true) {\n                    window.location.href = '/login';\n                }\n            }, 1000);\n            return {\n                success: false,\n                error: '登录已过期，请重新登录',\n                code: 'UNAUTHORIZED'\n            };\n        }\n    } else if (error instanceof Error) {\n        finalError = error.message;\n    } else if (error && typeof error === 'object') {\n        finalError = error.message || error.error || error.msg || finalError;\n        code = error.code;\n    } else if (typeof error === 'string') {\n        finalError = error;\n    }\n    // 使用自定义错误消息或原始错误消息\n    const displayError = errorMessage || finalError;\n    // 显示错误提示\n    if (showErrorToast) {\n        sonner__WEBPACK_IMPORTED_MODULE_0__.toast.error(displayError);\n    }\n    return {\n        success: false,\n        error: displayError,\n        code\n    };\n}\n/**\n * 批量API调用\n * 并行执行多个API调用，统一处理结果\n */ async function batchApiCall(apiCalls) {\n    let config = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { loadingKey } = config;\n    // 设置加载状态\n    if (loadingKey) {\n        LoadingManager.setLoading(loadingKey, true);\n    }\n    try {\n        const promises = Object.entries(apiCalls).map(async (param)=>{\n            let [key, apiFunction] = param;\n            const result = await apiCall(apiFunction, {\n                ...config,\n                loadingKey: undefined\n            });\n            return [\n                key,\n                result\n            ];\n        });\n        const results = await Promise.all(promises);\n        // 清除加载状态\n        if (loadingKey) {\n            LoadingManager.setLoading(loadingKey, false);\n        }\n        return Object.fromEntries(results);\n    } catch (error) {\n        // 清除加载状态\n        if (loadingKey) {\n            LoadingManager.setLoading(loadingKey, false);\n        }\n        throw error;\n    }\n}\n/**\n * 创建带有默认配置的API调用函数\n */ function createApiCaller(defaultConfig) {\n    return function(apiFunction) {\n        let config = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        return apiCall(apiFunction, {\n            ...defaultConfig,\n            ...config\n        });\n    };\n}\n/**\n * 常用的API调用配置预设\n */ const ApiPresets = {\n    // 静默调用（不显示任何提示）\n    silent: {\n        showSuccessToast: false,\n        showErrorToast: false\n    },\n    // 只显示错误提示\n    errorOnly: {\n        showSuccessToast: false,\n        showErrorToast: true\n    },\n    // 显示成功和错误提示\n    withToast: {\n        showSuccessToast: true,\n        showErrorToast: true\n    },\n    // 创建操作\n    create: {\n        showSuccessToast: true,\n        successMessage: '创建成功',\n        showErrorToast: true\n    },\n    // 更新操作\n    update: {\n        showSuccessToast: true,\n        successMessage: '更新成功',\n        showErrorToast: true\n    },\n    // 删除操作\n    delete: {\n        showSuccessToast: true,\n        successMessage: '删除成功',\n        showErrorToast: true\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api-helpers.ts\n"));

/***/ })

});