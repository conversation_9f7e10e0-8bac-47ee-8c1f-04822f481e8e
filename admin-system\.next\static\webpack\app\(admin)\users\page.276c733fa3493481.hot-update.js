"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(admin)/users/page",{

/***/ "(app-pages-browser)/./src/lib/api-helpers.ts":
/*!********************************!*\
  !*** ./src/lib/api-helpers.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiPresets: () => (/* binding */ ApiPresets),\n/* harmony export */   LoadingManager: () => (/* binding */ LoadingManager),\n/* harmony export */   apiCall: () => (/* binding */ apiCall),\n/* harmony export */   batchApiCall: () => (/* binding */ batchApiCall),\n/* harmony export */   createApiCaller: () => (/* binding */ createApiCaller)\n/* harmony export */ });\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _api_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/**\n * API调用辅助工具\n * 提供统一的API调用方式，自动处理错误和加载状态\n */ \n\nlet globalLoadingState = {};\nconst loadingListeners = [];\n// 加载状态管理器\nconst LoadingManager = {\n    setLoading (key, loading) {\n        globalLoadingState = {\n            ...globalLoadingState,\n            [key]: loading\n        };\n        loadingListeners.forEach((listener)=>listener(globalLoadingState));\n    },\n    isLoading (key) {\n        return globalLoadingState[key] || false;\n    },\n    subscribe (listener) {\n        loadingListeners.push(listener);\n        return ()=>{\n            const index = loadingListeners.indexOf(listener);\n            if (index > -1) {\n                loadingListeners.splice(index, 1);\n            }\n        };\n    },\n    getState () {\n        return {\n            ...globalLoadingState\n        };\n    }\n};\n/**\n * 统一的API调用函数\n * 自动处理加载状态、错误处理、成功提示等\n */ async function apiCall(apiFunction) {\n    let config = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { loadingKey, showSuccessToast = false, successMessage, showErrorToast = true, errorMessage, redirectOnAuth = true, retryCount = 0, retryDelay = 1000 } = config;\n    // 设置加载状态\n    if (loadingKey) {\n        LoadingManager.setLoading(loadingKey, true);\n    }\n    let lastError;\n    // 重试机制\n    for(let attempt = 0; attempt <= retryCount; attempt++){\n        try {\n            const response = await apiFunction();\n            // 清除加载状态\n            if (loadingKey) {\n                LoadingManager.setLoading(loadingKey, false);\n            }\n            // 检查响应格式\n            if (response && typeof response === 'object') {\n                // 标准API响应格式\n                if ('success' in response) {\n                    if (response.success) {\n                        // 成功响应\n                        if (showSuccessToast && (successMessage || response.message)) {\n                            sonner__WEBPACK_IMPORTED_MODULE_0__.toast.success(successMessage || response.message || '操作成功');\n                        }\n                        return {\n                            success: true,\n                            data: response.data\n                        };\n                    } else {\n                        // 业务错误 - 直接使用后端返回的错误消息\n                        const error = response.error || response.message || '操作失败';\n                        if (showErrorToast) {\n                            sonner__WEBPACK_IMPORTED_MODULE_0__.toast.error(errorMessage || error);\n                        }\n                        return {\n                            success: false,\n                            error: errorMessage || error,\n                            code: response.code\n                        };\n                    }\n                } else {\n                    // 直接返回数据的响应\n                    if (showSuccessToast && successMessage) {\n                        sonner__WEBPACK_IMPORTED_MODULE_0__.toast.success(successMessage);\n                    }\n                    return {\n                        success: true,\n                        data: response\n                    };\n                }\n            }\n            // 其他类型的响应\n            if (showSuccessToast && successMessage) {\n                sonner__WEBPACK_IMPORTED_MODULE_0__.toast.success(successMessage);\n            }\n            return {\n                success: true,\n                data: response\n            };\n        } catch (error) {\n            lastError = error;\n            // 如果是最后一次尝试或者是客户端错误，不再重试\n            if (attempt === retryCount || error instanceof _api_client__WEBPACK_IMPORTED_MODULE_1__.ApiClientError && error.statusCode < 500) {\n                break;\n            }\n            // 等待后重试\n            if (attempt < retryCount) {\n                await new Promise((resolve)=>setTimeout(resolve, retryDelay * (attempt + 1)));\n            }\n        }\n    }\n    // 清除加载状态\n    if (loadingKey) {\n        LoadingManager.setLoading(loadingKey, false);\n    }\n    // 处理错误\n    return handleApiError(lastError, {\n        showErrorToast,\n        errorMessage,\n        redirectOnAuth\n    });\n}\n/**\n * 处理API错误\n */ function handleApiError(error, config) {\n    const { showErrorToast = true, errorMessage, redirectOnAuth = true } = config;\n    let finalError = '操作失败'; // 最基本的错误提示，只在完全无法获取错误信息时使用\n    let code;\n    // ApiClientError - 直接使用后端返回的错误消息\n    if (error instanceof _api_client__WEBPACK_IMPORTED_MODULE_1__.ApiClientError) {\n        finalError = error.message; // 直接使用后端返回的消息\n        code = error.code;\n        // 处理认证错误 - 这里的消息也应该来自后端\n        if (error.statusCode === 401 && redirectOnAuth) {\n            if (showErrorToast) {\n                // 使用后端返回的错误消息，而不是硬编码\n                sonner__WEBPACK_IMPORTED_MODULE_0__.toast.error(error.message);\n            }\n            // 延迟跳转到登录页\n            setTimeout(()=>{\n                if (true) {\n                    window.location.href = '/login';\n                }\n            }, 1000);\n            return {\n                success: false,\n                error: error.message,\n                code: error.code || 'UNAUTHORIZED'\n            };\n        }\n    } else if (error instanceof Error) {\n        finalError = error.message;\n    } else if (error && typeof error === 'object') {\n        finalError = error.message || error.error || error.msg || '操作失败';\n        code = error.code;\n    } else if (typeof error === 'string') {\n        finalError = error;\n    }\n    // 使用自定义错误消息或原始错误消息\n    const displayError = errorMessage || finalError;\n    // 显示错误提示\n    if (showErrorToast) {\n        sonner__WEBPACK_IMPORTED_MODULE_0__.toast.error(displayError);\n    }\n    return {\n        success: false,\n        error: displayError,\n        code\n    };\n}\n/**\n * 批量API调用\n * 并行执行多个API调用，统一处理结果\n */ async function batchApiCall(apiCalls) {\n    let config = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { loadingKey } = config;\n    // 设置加载状态\n    if (loadingKey) {\n        LoadingManager.setLoading(loadingKey, true);\n    }\n    try {\n        const promises = Object.entries(apiCalls).map(async (param)=>{\n            let [key, apiFunction] = param;\n            const result = await apiCall(apiFunction, {\n                ...config,\n                loadingKey: undefined\n            });\n            return [\n                key,\n                result\n            ];\n        });\n        const results = await Promise.all(promises);\n        // 清除加载状态\n        if (loadingKey) {\n            LoadingManager.setLoading(loadingKey, false);\n        }\n        return Object.fromEntries(results);\n    } catch (error) {\n        // 清除加载状态\n        if (loadingKey) {\n            LoadingManager.setLoading(loadingKey, false);\n        }\n        throw error;\n    }\n}\n/**\n * 创建带有默认配置的API调用函数\n */ function createApiCaller(defaultConfig) {\n    return function(apiFunction) {\n        let config = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        return apiCall(apiFunction, {\n            ...defaultConfig,\n            ...config\n        });\n    };\n}\n/**\n * 常用的API调用配置预设\n */ const ApiPresets = {\n    // 静默调用（不显示任何提示）\n    silent: {\n        showSuccessToast: false,\n        showErrorToast: false\n    },\n    // 只显示错误提示\n    errorOnly: {\n        showSuccessToast: false,\n        showErrorToast: true\n    },\n    // 显示成功和错误提示\n    withToast: {\n        showSuccessToast: true,\n        showErrorToast: true\n    },\n    // 创建操作\n    create: {\n        showSuccessToast: true,\n        successMessage: '创建成功',\n        showErrorToast: true\n    },\n    // 更新操作\n    update: {\n        showSuccessToast: true,\n        successMessage: '更新成功',\n        showErrorToast: true\n    },\n    // 删除操作\n    delete: {\n        showSuccessToast: true,\n        successMessage: '删除成功',\n        showErrorToast: true\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api-helpers.ts\n"));

/***/ })

});