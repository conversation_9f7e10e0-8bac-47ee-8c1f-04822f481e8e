/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/users/route";
exports.ids = ["app/api/users/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fusers%2Froute&page=%2Fapi%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Froute.ts&appDir=E%3A%5C7mouthMission%5CuniFigma%5Cadmin-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C7mouthMission%5CuniFigma%5Cadmin-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fusers%2Froute&page=%2Fapi%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Froute.ts&appDir=E%3A%5C7mouthMission%5CuniFigma%5Cadmin-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C7mouthMission%5CuniFigma%5Cadmin-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   handler: () => (/* binding */ handler),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/request-meta */ \"(rsc)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/request-meta.js\");\n/* harmony import */ var next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/dist/server/lib/trace/tracer */ \"(rsc)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/trace/tracer.js\");\n/* harmony import */ var next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/dist/shared/lib/router/utils/app-paths */ \"next/dist/shared/lib/router/utils/app-paths\");\n/* harmony import */ var next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/dist/server/base-http/node */ \"(rsc)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/base-http/node.js\");\n/* harmony import */ var next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next/dist/server/web/spec-extension/adapters/next-request */ \"(rsc)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/web/spec-extension/adapters/next-request.js\");\n/* harmony import */ var next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/dist/server/lib/trace/constants */ \"(rsc)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/trace/constants.js\");\n/* harmony import */ var next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__);\n/* harmony import */ var next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/dist/server/instrumentation/utils */ \"(rsc)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/instrumentation/utils.js\");\n/* harmony import */ var next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next/dist/server/send-response */ \"(rsc)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/send-response.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next/dist/server/web/utils */ \"(rsc)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/web/utils.js\");\n/* harmony import */ var next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__);\n/* harmony import */ var next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! next/dist/server/lib/cache-control */ \"(rsc)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/lib/cache-control.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! next/dist/lib/constants */ \"(rsc)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/lib/constants.js\");\n/* harmony import */ var next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__);\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! next/dist/shared/lib/no-fallback-error.external */ \"next/dist/shared/lib/no-fallback-error.external\");\n/* harmony import */ var next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14___default = /*#__PURE__*/__webpack_require__.n(next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__);\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! next/dist/server/response-cache */ \"(rsc)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/response-cache/index.js\");\n/* harmony import */ var next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__);\n/* harmony import */ var E_7mouthMission_uniFigma_admin_system_src_app_api_users_route_ts__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./src/app/api/users/route.ts */ \"(rsc)/./src/app/api/users/route.ts\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/users/route\",\n        pathname: \"/api/users\",\n        filename: \"route\",\n        bundlePath: \"app/api/users/route\"\n    },\n    distDir: \".next\" || 0,\n    projectDir:  false || '',\n    resolvedPagePath: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\api\\\\users\\\\route.ts\",\n    nextConfigOutput,\n    userland: E_7mouthMission_uniFigma_admin_system_src_app_api_users_route_ts__WEBPACK_IMPORTED_MODULE_16__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\nasync function handler(req, res, ctx) {\n    var _nextConfig_experimental;\n    let srcPage = \"/api/users/route\";\n    // turbopack doesn't normalize `/index` in the page name\n    // so we need to to process dynamic routes properly\n    // TODO: fix turbopack providing differing value from webpack\n    if (false) {} else if (srcPage === '/index') {\n        // we always normalize /index specifically\n        srcPage = '/';\n    }\n    const multiZoneDraftMode = \"false\";\n    const prepareResult = await routeModule.prepare(req, res, {\n        srcPage,\n        multiZoneDraftMode\n    });\n    if (!prepareResult) {\n        res.statusCode = 400;\n        res.end('Bad Request');\n        ctx.waitUntil == null ? void 0 : ctx.waitUntil.call(ctx, Promise.resolve());\n        return null;\n    }\n    const { buildId, params, nextConfig, isDraftMode, prerenderManifest, routerServerContext, isOnDemandRevalidate, revalidateOnlyGenerated, resolvedPathname } = prepareResult;\n    const normalizedSrcPage = (0,next_dist_shared_lib_router_utils_app_paths__WEBPACK_IMPORTED_MODULE_5__.normalizeAppPath)(srcPage);\n    let isIsr = Boolean(prerenderManifest.dynamicRoutes[normalizedSrcPage] || prerenderManifest.routes[resolvedPathname]);\n    if (isIsr && !isDraftMode) {\n        const isPrerendered = Boolean(prerenderManifest.routes[resolvedPathname]);\n        const prerenderInfo = prerenderManifest.dynamicRoutes[normalizedSrcPage];\n        if (prerenderInfo) {\n            if (prerenderInfo.fallback === false && !isPrerendered) {\n                throw new next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError();\n            }\n        }\n    }\n    let cacheKey = null;\n    if (isIsr && !routeModule.isDev && !isDraftMode) {\n        cacheKey = resolvedPathname;\n        // ensure /index and / is normalized to one key\n        cacheKey = cacheKey === '/index' ? '/' : cacheKey;\n    }\n    const supportsDynamicResponse = // If we're in development, we always support dynamic HTML\n    routeModule.isDev === true || // If this is not SSG or does not have static paths, then it supports\n    // dynamic HTML.\n    !isIsr;\n    // This is a revalidation request if the request is for a static\n    // page and it is not being resumed from a postponed render and\n    // it is not a dynamic RSC request then it is a revalidation\n    // request.\n    const isRevalidate = isIsr && !supportsDynamicResponse;\n    const method = req.method || 'GET';\n    const tracer = (0,next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.getTracer)();\n    const activeSpan = tracer.getActiveScopeSpan();\n    const context = {\n        params,\n        prerenderManifest,\n        renderOpts: {\n            experimental: {\n                dynamicIO: Boolean(nextConfig.experimental.dynamicIO),\n                authInterrupts: Boolean(nextConfig.experimental.authInterrupts)\n            },\n            supportsDynamicResponse,\n            incrementalCache: (0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'incrementalCache'),\n            cacheLifeProfiles: (_nextConfig_experimental = nextConfig.experimental) == null ? void 0 : _nextConfig_experimental.cacheLife,\n            isRevalidate,\n            waitUntil: ctx.waitUntil,\n            onClose: (cb)=>{\n                res.on('close', cb);\n            },\n            onAfterTaskError: undefined,\n            onInstrumentationRequestError: (error, _request, errorContext)=>routeModule.onRequestError(req, error, errorContext, routerServerContext)\n        },\n        sharedContext: {\n            buildId\n        }\n    };\n    const nodeNextReq = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextRequest(req);\n    const nodeNextRes = new next_dist_server_base_http_node__WEBPACK_IMPORTED_MODULE_6__.NodeNextResponse(res);\n    const nextReq = next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.NextRequestAdapter.fromNodeNextRequest(nodeNextReq, (0,next_dist_server_web_spec_extension_adapters_next_request__WEBPACK_IMPORTED_MODULE_7__.signalFromNodeResponse)(res));\n    try {\n        const invokeRouteModule = async (span)=>{\n            return routeModule.handle(nextReq, context).finally(()=>{\n                if (!span) return;\n                span.setAttributes({\n                    'http.status_code': res.statusCode,\n                    'next.rsc': false\n                });\n                const rootSpanAttributes = tracer.getRootSpanAttributes();\n                // We were unable to get attributes, probably OTEL is not enabled\n                if (!rootSpanAttributes) {\n                    return;\n                }\n                if (rootSpanAttributes.get('next.span_type') !== next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest) {\n                    console.warn(`Unexpected root span type '${rootSpanAttributes.get('next.span_type')}'. Please report this Next.js issue https://github.com/vercel/next.js`);\n                    return;\n                }\n                const route = rootSpanAttributes.get('next.route');\n                if (route) {\n                    const name = `${method} ${route}`;\n                    span.setAttributes({\n                        'next.route': route,\n                        'http.route': route,\n                        'next.span_name': name\n                    });\n                    span.updateName(name);\n                } else {\n                    span.updateName(`${method} ${req.url}`);\n                }\n            });\n        };\n        const handleResponse = async (currentSpan)=>{\n            var _cacheEntry_value;\n            const responseGenerator = async ({ previousCacheEntry })=>{\n                try {\n                    if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isOnDemandRevalidate && revalidateOnlyGenerated && !previousCacheEntry) {\n                        res.statusCode = 404;\n                        // on-demand revalidate always sets this header\n                        res.setHeader('x-nextjs-cache', 'REVALIDATED');\n                        res.end('This page could not be found');\n                        return null;\n                    }\n                    const response = await invokeRouteModule(currentSpan);\n                    req.fetchMetrics = context.renderOpts.fetchMetrics;\n                    let pendingWaitUntil = context.renderOpts.pendingWaitUntil;\n                    // Attempt using provided waitUntil if available\n                    // if it's not we fallback to sendResponse's handling\n                    if (pendingWaitUntil) {\n                        if (ctx.waitUntil) {\n                            ctx.waitUntil(pendingWaitUntil);\n                            pendingWaitUntil = undefined;\n                        }\n                    }\n                    const cacheTags = context.renderOpts.collectedTags;\n                    // If the request is for a static response, we can cache it so long\n                    // as it's not edge.\n                    if (isIsr) {\n                        const blob = await response.blob();\n                        // Copy the headers from the response.\n                        const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.toNodeOutgoingHttpHeaders)(response.headers);\n                        if (cacheTags) {\n                            headers[next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER] = cacheTags;\n                        }\n                        if (!headers['content-type'] && blob.type) {\n                            headers['content-type'] = blob.type;\n                        }\n                        const revalidate = typeof context.renderOpts.collectedRevalidate === 'undefined' || context.renderOpts.collectedRevalidate >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? false : context.renderOpts.collectedRevalidate;\n                        const expire = typeof context.renderOpts.collectedExpire === 'undefined' || context.renderOpts.collectedExpire >= next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.INFINITE_CACHE ? undefined : context.renderOpts.collectedExpire;\n                        // Create the cache entry for the response.\n                        const cacheEntry = {\n                            value: {\n                                kind: next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE,\n                                status: response.status,\n                                body: Buffer.from(await blob.arrayBuffer()),\n                                headers\n                            },\n                            cacheControl: {\n                                revalidate,\n                                expire\n                            }\n                        };\n                        return cacheEntry;\n                    } else {\n                        // send response without caching if not ISR\n                        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, response, context.renderOpts.pendingWaitUntil);\n                        return null;\n                    }\n                } catch (err) {\n                    // if this is a background revalidate we need to report\n                    // the request error here as it won't be bubbled\n                    if (previousCacheEntry == null ? void 0 : previousCacheEntry.isStale) {\n                        await routeModule.onRequestError(req, err, {\n                            routerKind: 'App Router',\n                            routePath: srcPage,\n                            routeType: 'route',\n                            revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                                isRevalidate,\n                                isOnDemandRevalidate\n                            })\n                        }, routerServerContext);\n                    }\n                    throw err;\n                }\n            };\n            const cacheEntry = await routeModule.handleResponse({\n                req,\n                nextConfig,\n                cacheKey,\n                routeKind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n                isFallback: false,\n                prerenderManifest,\n                isRoutePPREnabled: false,\n                isOnDemandRevalidate,\n                revalidateOnlyGenerated,\n                responseGenerator,\n                waitUntil: ctx.waitUntil\n            });\n            // we don't create a cacheEntry for ISR\n            if (!isIsr) {\n                return null;\n            }\n            if ((cacheEntry == null ? void 0 : (_cacheEntry_value = cacheEntry.value) == null ? void 0 : _cacheEntry_value.kind) !== next_dist_server_response_cache__WEBPACK_IMPORTED_MODULE_15__.CachedRouteKind.APP_ROUTE) {\n                var _cacheEntry_value1;\n                throw Object.defineProperty(new Error(`Invariant: app-route received invalid cache entry ${cacheEntry == null ? void 0 : (_cacheEntry_value1 = cacheEntry.value) == null ? void 0 : _cacheEntry_value1.kind}`), \"__NEXT_ERROR_CODE\", {\n                    value: \"E701\",\n                    enumerable: false,\n                    configurable: true\n                });\n            }\n            if (!(0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode')) {\n                res.setHeader('x-nextjs-cache', isOnDemandRevalidate ? 'REVALIDATED' : cacheEntry.isMiss ? 'MISS' : cacheEntry.isStale ? 'STALE' : 'HIT');\n            }\n            // Draft mode should never be cached\n            if (isDraftMode) {\n                res.setHeader('Cache-Control', 'private, no-cache, no-store, max-age=0, must-revalidate');\n            }\n            const headers = (0,next_dist_server_web_utils__WEBPACK_IMPORTED_MODULE_11__.fromNodeOutgoingHttpHeaders)(cacheEntry.value.headers);\n            if (!((0,next_dist_server_request_meta__WEBPACK_IMPORTED_MODULE_3__.getRequestMeta)(req, 'minimalMode') && isIsr)) {\n                headers.delete(next_dist_lib_constants__WEBPACK_IMPORTED_MODULE_13__.NEXT_CACHE_TAGS_HEADER);\n            }\n            // If cache control is already set on the response we don't\n            // override it to allow users to customize it via next.config\n            if (cacheEntry.cacheControl && !res.getHeader('Cache-Control') && !headers.get('Cache-Control')) {\n                headers.set('Cache-Control', (0,next_dist_server_lib_cache_control__WEBPACK_IMPORTED_MODULE_12__.getCacheControlHeader)(cacheEntry.cacheControl));\n            }\n            await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(cacheEntry.value.body, {\n                headers,\n                status: cacheEntry.value.status || 200\n            }));\n            return null;\n        };\n        // TODO: activeSpan code path is for when wrapped by\n        // next-server can be removed when this is no longer used\n        if (activeSpan) {\n            await handleResponse(activeSpan);\n        } else {\n            await tracer.withPropagatedContext(req.headers, ()=>tracer.trace(next_dist_server_lib_trace_constants__WEBPACK_IMPORTED_MODULE_8__.BaseServerSpan.handleRequest, {\n                    spanName: `${method} ${req.url}`,\n                    kind: next_dist_server_lib_trace_tracer__WEBPACK_IMPORTED_MODULE_4__.SpanKind.SERVER,\n                    attributes: {\n                        'http.method': method,\n                        'http.target': req.url\n                    }\n                }, handleResponse));\n        }\n    } catch (err) {\n        // if we aren't wrapped by base-server handle here\n        if (!activeSpan && !(err instanceof next_dist_shared_lib_no_fallback_error_external__WEBPACK_IMPORTED_MODULE_14__.NoFallbackError)) {\n            await routeModule.onRequestError(req, err, {\n                routerKind: 'App Router',\n                routePath: normalizedSrcPage,\n                routeType: 'route',\n                revalidateReason: (0,next_dist_server_instrumentation_utils__WEBPACK_IMPORTED_MODULE_9__.getRevalidateReason)({\n                    isRevalidate,\n                    isOnDemandRevalidate\n                })\n            });\n        }\n        // rethrow so that we can handle serving error page\n        // If this is during static generation, throw the error again.\n        if (isIsr) throw err;\n        // Otherwise, send a 500 response.\n        await (0,next_dist_server_send_response__WEBPACK_IMPORTED_MODULE_10__.sendResponse)(nodeNextReq, nodeNextRes, new Response(null, {\n            status: 500\n        }));\n        return null;\n    }\n}\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fusers%2Froute&page=%2Fapi%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Froute.ts&appDir=E%3A%5C7mouthMission%5CuniFigma%5Cadmin-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C7mouthMission%5CuniFigma%5Cadmin-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!\n");

/***/ }),

/***/ "(rsc)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/users/route.ts":
/*!************************************!*\
  !*** ./src/app/api/users/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var _lib_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _lib_api_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/api-utils */ \"(rsc)/./src/lib/api-utils.ts\");\n\n\n// GET /api/users - Get all users with filters (admin only, admin web client only)\nasync function GET(request) {\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.withAdminAccess)()(request, async (req, user, session, clientInfo)=>{\n        try {\n            const url = new URL(request.url);\n            const searchParams = url.searchParams;\n            // Parse pagination parameters\n            const page = parseInt(searchParams.get('page') || '1');\n            const limit = parseInt(searchParams.get('limit') || '20');\n            // Parse filter parameters\n            const search = searchParams.get('search') || undefined;\n            const role = searchParams.get('role') || undefined;\n            const vip_level = searchParams.get('vip_level') || undefined;\n            const is_active = searchParams.get('is_active') ? searchParams.get('is_active') === 'true' : undefined;\n            const is_banned = searchParams.get('is_banned') ? searchParams.get('is_banned') === 'true' : undefined;\n            const registration_source = searchParams.get('registration_source') || undefined;\n            const sort_by = searchParams.get('sort_by') || 'created_at';\n            const sort_order = searchParams.get('sort_order') || 'desc';\n            const { users, total } = await _lib_auth__WEBPACK_IMPORTED_MODULE_0__.UserManager.getUsersWithFilters({\n                page,\n                limit,\n                search,\n                role,\n                vip_level,\n                is_active,\n                is_banned,\n                registration_source,\n                sort_by,\n                sort_order\n            });\n            const totalPages = Math.ceil(total / limit);\n            await (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.logAuditEvent)({\n                userId: user.id,\n                action: 'USERS_VIEWED',\n                resource: 'users',\n                details: {\n                    page,\n                    limit,\n                    total,\n                    filters: {\n                        search,\n                        role,\n                        vip_level,\n                        is_active,\n                        is_banned,\n                        registration_source\n                    }\n                }\n            });\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.successResponse)(users, 'Users retrieved successfully', {\n                page,\n                limit,\n                total,\n                totalPages\n            });\n        } catch (error) {\n            console.error('Get users error:', error);\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.errorResponse)('Failed to retrieve users', 500);\n        }\n    });\n}\n// POST /api/users - Create new user (admin only, admin web client only)\nasync function POST(request) {\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.withAdminAccess)()(request, async (req, user, session, clientInfo)=>{\n        try {\n            const body = await request.json();\n            // Validate required fields\n            const { username, email, password, full_name, role } = body;\n            if (!username || !email || !password) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.errorResponse)('Username, email, and password are required', 400);\n            }\n            // Check if user already exists\n            const existingUser = await _lib_auth__WEBPACK_IMPORTED_MODULE_0__.UserManager.getUserByEmail(email);\n            if (existingUser) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.errorResponse)('User with this email already exists', 409);\n            }\n            const existingUsername = await _lib_auth__WEBPACK_IMPORTED_MODULE_0__.UserManager.getUserByUsername(username);\n            if (existingUsername) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.errorResponse)('Username already taken', 409);\n            }\n            // Create user\n            const newUser = await _lib_auth__WEBPACK_IMPORTED_MODULE_0__.UserManager.createUser({\n                username,\n                email,\n                password,\n                full_name,\n                role: role || 'user'\n            });\n            await (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.logAuditEvent)({\n                userId: user.id,\n                action: 'USER_CREATED',\n                resource: 'users',\n                resourceId: newUser.id.toString(),\n                details: {\n                    createdUser: {\n                        id: newUser.id,\n                        username: newUser.username,\n                        email: newUser.email,\n                        role: newUser.role\n                    }\n                }\n            });\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.successResponse)({\n                id: newUser.id,\n                username: newUser.username,\n                email: newUser.email,\n                full_name: newUser.full_name,\n                role: newUser.role,\n                avatar_url: newUser.avatar_url,\n                is_active: newUser.is_active,\n                created_at: newUser.created_at\n            }, 'User created successfully');\n        } catch (error) {\n            console.error('Create user error:', error);\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_1__.errorResponse)('Failed to create user', 500);\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/users/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/api-utils.ts":
/*!******************************!*\
  !*** ./src/lib/api-utils.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiError: () => (/* binding */ ApiError),\n/* harmony export */   errorResponse: () => (/* binding */ errorResponse),\n/* harmony export */   getClientIP: () => (/* binding */ getClientIP),\n/* harmony export */   getPaginationParams: () => (/* binding */ getPaginationParams),\n/* harmony export */   logAuditEvent: () => (/* binding */ logAuditEvent),\n/* harmony export */   rateLimit: () => (/* binding */ rateLimit),\n/* harmony export */   successResponse: () => (/* binding */ successResponse),\n/* harmony export */   validateRequest: () => (/* binding */ validateRequest),\n/* harmony export */   validationErrorResponse: () => (/* binding */ validationErrorResponse),\n/* harmony export */   validators: () => (/* binding */ validators),\n/* harmony export */   withAdminAccess: () => (/* binding */ withAdminAccess),\n/* harmony export */   withAuth: () => (/* binding */ withAuth),\n/* harmony export */   withRole: () => (/* binding */ withRole),\n/* harmony export */   withUserAccess: () => (/* binding */ withUserAccess)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/server.js\");\n/* harmony import */ var _auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./auth */ \"(rsc)/./src/lib/auth.ts\");\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n/* harmony import */ var _client_detection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./client-detection */ \"(rsc)/./src/lib/client-detection.ts\");\n\n\n\n\n// API Error types\nclass ApiError extends Error {\n    constructor(statusCode, message, code){\n        super(message), this.statusCode = statusCode, this.code = code;\n        this.name = 'ApiError';\n    }\n}\n// Success response helper\nfunction successResponse(data, message, pagination) {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        success: true,\n        data,\n        message,\n        pagination\n    });\n}\n// Error response helper\nfunction errorResponse(error, statusCode = 500, code) {\n    const message = error instanceof Error ? error.message : error;\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        success: false,\n        error: message,\n        code\n    }, {\n        status: statusCode\n    });\n}\n// Validation error response\nfunction validationErrorResponse(errors) {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        success: false,\n        error: 'Validation failed',\n        data: errors\n    }, {\n        status: 400\n    });\n}\n// Authentication middleware with client detection\nasync function withAuth(request, handler) {\n    try {\n        // 检测客户端信息\n        const clientInfo = _client_detection__WEBPACK_IMPORTED_MODULE_3__.ClientDetector.getClientInfo(request);\n        const authHeader = request.headers.get('authorization');\n        if (!authHeader || !authHeader.startsWith('Bearer ')) {\n            return errorResponse('缺少或无效的授权头', 401);\n        }\n        const token = authHeader.substring(7);\n        const auth = await (0,_auth__WEBPACK_IMPORTED_MODULE_1__.verifyAuth)(token);\n        if (!auth) {\n            return errorResponse('无效或过期的令牌', 401);\n        }\n        // 检查客户端权限\n        if (!_client_detection__WEBPACK_IMPORTED_MODULE_3__.ClientDetector.isTrustedClient(clientInfo.type)) {\n            return errorResponse('不受信任的客户端', 403);\n        }\n        // Log API access with client info\n        await logAuditEvent({\n            userId: auth.user.id,\n            action: 'API_ACCESS',\n            resource: request.nextUrl.pathname,\n            details: {\n                method: request.method,\n                userAgent: request.headers.get('user-agent'),\n                clientType: clientInfo.type,\n                clientVersion: clientInfo.version,\n                clientPlatform: clientInfo.platform\n            },\n            ipAddress: clientInfo.ipAddress\n        });\n        return handler(request, auth.user, auth.session, clientInfo);\n    } catch (error) {\n        console.error('Auth middleware error:', error);\n        return errorResponse('服务器内部错误', 500);\n    }\n}\n// Role-based access control with client detection\nfunction withRole(roles) {\n    return async (request, handler)=>{\n        return withAuth(request, async (req, user, session, clientInfo)=>{\n            if (!roles.includes(user.role)) {\n                return errorResponse('权限不足', 403);\n            }\n            return handler(req, user, session, clientInfo);\n        });\n    };\n}\n// Admin-only access control (only for admin web clients)\nfunction withAdminAccess() {\n    return async (request, handler)=>{\n        return withAuth(request, async (req, user, session, clientInfo)=>{\n            // 检查是否为管理员角色\n            if (user.role !== 'admin') {\n                return errorResponse('需要管理员权限', 403);\n            }\n            // 检查是否为管理端客户端\n            if (!_client_detection__WEBPACK_IMPORTED_MODULE_3__.ClientPermissionChecker.canAccessAdminAPI(clientInfo.type)) {\n                return errorResponse('此客户端无法访问管理API', 403);\n            }\n            return handler(req, user, session, clientInfo);\n        });\n    };\n}\n// User API access control (for both admin and user clients)\nfunction withUserAccess() {\n    return async (request, handler)=>{\n        return withAuth(request, async (req, user, session, clientInfo)=>{\n            // 检查客户端是否可以访问用户API\n            if (!_client_detection__WEBPACK_IMPORTED_MODULE_3__.ClientPermissionChecker.canAccessUserAPI(clientInfo.type)) {\n                return errorResponse('此客户端无法访问用户API', 403);\n            }\n            return handler(req, user, session, clientInfo);\n        });\n    };\n}\n// Request validation\nfunction validateRequest(data, schema) {\n    const errors = {};\n    const validatedData = {};\n    for (const [field, validator] of Object.entries(schema)){\n        const error = validator(data[field]);\n        if (error) {\n            errors[field] = error;\n        } else {\n            validatedData[field] = data[field];\n        }\n    }\n    return {\n        isValid: Object.keys(errors).length === 0,\n        errors,\n        data: validatedData\n    };\n}\n// Common validators\nconst validators = {\n    required: (value)=>{\n        if (value === undefined || value === null || value === '') {\n            return '此字段为必填项';\n        }\n        return null;\n    },\n    email: (value)=>{\n        if (!value) return null;\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        return emailRegex.test(value) ? null : '邮箱格式无效';\n    },\n    minLength: (min)=>(value)=>{\n            if (!value) return null;\n            return value.length >= min ? null : `至少需要 ${min} 个字符`;\n        },\n    maxLength: (max)=>(value)=>{\n            if (!value) return null;\n            return value.length <= max ? null : `不能超过 ${max} 个字符`;\n        },\n    oneOf: (options)=>(value)=>{\n            if (!value) return null;\n            return options.includes(value) ? null : `必须是以下选项之一：${options.join(', ')}`;\n        },\n    numeric: (value)=>{\n        if (value === undefined || value === null) return null;\n        return !isNaN(Number(value)) ? null : '必须是数字';\n    },\n    boolean: (value)=>{\n        if (value === undefined || value === null) return null;\n        return typeof value === 'boolean' ? null : '必须是布尔值';\n    }\n};\n// Pagination helper\nfunction getPaginationParams(request) {\n    const url = new URL(request.url);\n    const page = Math.max(1, parseInt(url.searchParams.get('page') || '1'));\n    const limit = Math.min(100, Math.max(1, parseInt(url.searchParams.get('limit') || '10')));\n    const offset = (page - 1) * limit;\n    return {\n        page,\n        limit,\n        offset\n    };\n}\n// Get client IP address\nfunction getClientIP(request) {\n    const forwarded = request.headers.get('x-forwarded-for');\n    const realIP = request.headers.get('x-real-ip');\n    if (forwarded) {\n        return forwarded.split(',')[0].trim();\n    }\n    if (realIP) {\n        return realIP;\n    }\n    return 'unknown';\n}\nasync function logAuditEvent(data) {\n    try {\n        await _database__WEBPACK_IMPORTED_MODULE_2__.db.query(`INSERT INTO audit_logs (user_id, action, resource, resource_id, details, ip_address, user_agent)\n       VALUES (?, ?, ?, ?, ?, ?, ?)`, [\n            data.userId || null,\n            data.action,\n            data.resource || null,\n            data.resourceId || null,\n            data.details ? JSON.stringify(data.details) : null,\n            data.ipAddress || null,\n            data.userAgent || null\n        ]);\n    } catch (error) {\n        console.error('Failed to log audit event:', error);\n    }\n}\n// Rate limiting helper (simple in-memory implementation)\nconst rateLimitStore = new Map();\nfunction rateLimit(key, maxRequests = 100, windowMs = 60000) {\n    const now = Date.now();\n    const record = rateLimitStore.get(key);\n    if (!record || now > record.resetTime) {\n        rateLimitStore.set(key, {\n            count: 1,\n            resetTime: now + windowMs\n        });\n        return true;\n    }\n    if (record.count >= maxRequests) {\n        return false;\n    }\n    record.count++;\n    return true;\n}\n// Clean up expired rate limit records\nsetInterval(()=>{\n    const now = Date.now();\n    for (const [key, record] of rateLimitStore.entries()){\n        if (now > record.resetTime) {\n            rateLimitStore.delete(key);\n        }\n    }\n}, 60000); // Clean up every minute\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api-utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth.ts":
/*!*************************!*\
  !*** ./src/lib/auth.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   JWTUtils: () => (/* binding */ JWTUtils),\n/* harmony export */   PasswordUtils: () => (/* binding */ PasswordUtils),\n/* harmony export */   SessionManager: () => (/* binding */ SessionManager),\n/* harmony export */   UserManager: () => (/* binding */ UserManager),\n/* harmony export */   verifyAuth: () => (/* binding */ verifyAuth)\n/* harmony export */ });\n/* harmony import */ var bcryptjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! bcryptjs */ \"(rsc)/./node_modules/.pnpm/bcryptjs@3.0.2/node_modules/bcryptjs/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/.pnpm/jsonwebtoken@9.0.2/node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var crypto_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! crypto-js */ \"(rsc)/./node_modules/.pnpm/crypto-js@4.2.0/node_modules/crypto-js/index.js\");\n/* harmony import */ var crypto_js__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(crypto_js__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _database__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./database */ \"(rsc)/./src/lib/database.ts\");\n\n\n\n\n// Constants\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';\nconst SALT_ROUNDS = 12;\nconst SESSION_DURATION = 7 * 24 * 60 * 60 * 1000; // 7 days\n// Password utilities\nclass PasswordUtils {\n    static async hash(password) {\n        return bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].hash(password, SALT_ROUNDS);\n    }\n    static async verify(password, hash) {\n        return bcryptjs__WEBPACK_IMPORTED_MODULE_0__[\"default\"].compare(password, hash);\n    }\n    static generateSecurePassword(length = 12) {\n        const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';\n        let password = '';\n        for(let i = 0; i < length; i++){\n            password += charset.charAt(Math.floor(Math.random() * charset.length));\n        }\n        return password;\n    }\n}\n// JWT utilities\nclass JWTUtils {\n    static sign(payload) {\n        return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().sign(payload, JWT_SECRET, {\n            expiresIn: '7d'\n        });\n    }\n    static verify(token) {\n        try {\n            return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().verify(token, JWT_SECRET);\n        } catch (error) {\n            return null;\n        }\n    }\n    static decode(token) {\n        try {\n            return jsonwebtoken__WEBPACK_IMPORTED_MODULE_1___default().decode(token);\n        } catch (error) {\n            return null;\n        }\n    }\n}\n// Session management\nclass SessionManager {\n    static generateSessionId() {\n        return crypto_js__WEBPACK_IMPORTED_MODULE_2___default().lib.WordArray.random(32).toString();\n    }\n    static async createSession(userId) {\n        const sessionId = this.generateSessionId();\n        const expiresAt = new Date(Date.now() + SESSION_DURATION);\n        await _database__WEBPACK_IMPORTED_MODULE_3__.db.query('INSERT INTO sessions (id, user_id, expires_at) VALUES (?, ?, ?)', [\n            sessionId,\n            userId,\n            expiresAt\n        ]);\n        return sessionId;\n    }\n    static async getSession(sessionId) {\n        return _database__WEBPACK_IMPORTED_MODULE_3__.db.queryOne('SELECT * FROM sessions WHERE id = ? AND expires_at > NOW()', [\n            sessionId\n        ]);\n    }\n    static async deleteSession(sessionId) {\n        await _database__WEBPACK_IMPORTED_MODULE_3__.db.query('DELETE FROM sessions WHERE id = ?', [\n            sessionId\n        ]);\n    }\n    static async deleteUserSessions(userId) {\n        await _database__WEBPACK_IMPORTED_MODULE_3__.db.query('DELETE FROM sessions WHERE user_id = ?', [\n            userId\n        ]);\n    }\n    static async cleanExpiredSessions() {\n        await _database__WEBPACK_IMPORTED_MODULE_3__.db.query('DELETE FROM sessions WHERE expires_at <= NOW()');\n    }\n}\n// User management\nclass UserManager {\n    static async createUser(userData) {\n        const passwordHash = await PasswordUtils.hash(userData.password);\n        const result = await _database__WEBPACK_IMPORTED_MODULE_3__.db.query(`INSERT INTO users (username, email, password_hash, full_name, role, registration_source, phone, gender, birth_date)\n       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`, [\n            userData.username,\n            userData.email,\n            passwordHash,\n            userData.full_name || null,\n            userData.role || 'user',\n            userData.registration_source || 'web',\n            userData.phone || null,\n            userData.gender || null,\n            userData.birth_date || null\n        ]);\n        const user = await this.getUserById(result.insertId);\n        if (!user) {\n            throw new Error('Failed to create user');\n        }\n        return user;\n    }\n    static async getUserById(id) {\n        return _database__WEBPACK_IMPORTED_MODULE_3__.db.queryOne('SELECT id, username, email, full_name, role, avatar_url, is_active, last_login, created_at, updated_at FROM users WHERE id = ?', [\n            id\n        ]);\n    }\n    static async getUserByEmail(email) {\n        return _database__WEBPACK_IMPORTED_MODULE_3__.db.queryOne('SELECT id, username, email, full_name, role, avatar_url, is_active, last_login, created_at, updated_at FROM users WHERE email = ?', [\n            email\n        ]);\n    }\n    static async getUserByUsername(username) {\n        return _database__WEBPACK_IMPORTED_MODULE_3__.db.queryOne('SELECT id, username, email, full_name, role, avatar_url, is_active, last_login, created_at, updated_at FROM users WHERE username = ?', [\n            username\n        ]);\n    }\n    static async authenticateUser(emailOrUsername, password) {\n        const user = await _database__WEBPACK_IMPORTED_MODULE_3__.db.queryOne('SELECT * FROM users WHERE (email = ? OR username = ?) AND is_active = true', [\n            emailOrUsername,\n            emailOrUsername\n        ]);\n        if (!user) {\n            return null;\n        }\n        const isValidPassword = await PasswordUtils.verify(password, user.password_hash);\n        if (!isValidPassword) {\n            return null;\n        }\n        // Update last login and login count\n        await _database__WEBPACK_IMPORTED_MODULE_3__.db.query('UPDATE users SET last_login = NOW(), login_count = login_count + 1 WHERE id = ?', [\n            user.id\n        ]);\n        // Remove password hash from returned user\n        const { password_hash, ...userWithoutPassword } = user;\n        return userWithoutPassword;\n    }\n    // Ban/Unban user methods\n    static async banUser(userId, reason, bannedBy, expiresAt) {\n        await _database__WEBPACK_IMPORTED_MODULE_3__.db.query(`UPDATE users SET\n       is_banned = true,\n       ban_reason = ?,\n       banned_by = ?,\n       banned_at = NOW(),\n       ban_expires_at = ?\n       WHERE id = ?`, [\n            reason,\n            bannedBy,\n            expiresAt || null,\n            userId\n        ]);\n    }\n    static async unbanUser(userId) {\n        await _database__WEBPACK_IMPORTED_MODULE_3__.db.query(`UPDATE users SET\n       is_banned = false,\n       ban_reason = NULL,\n       banned_by = NULL,\n       banned_at = NULL,\n       ban_expires_at = NULL\n       WHERE id = ?`, [\n            userId\n        ]);\n    }\n    // VIP management methods\n    static async setVipLevel(userId, level, expiresAt) {\n        await _database__WEBPACK_IMPORTED_MODULE_3__.db.query('UPDATE users SET vip_level = ?, vip_expires_at = ? WHERE id = ?', [\n            level,\n            expiresAt || null,\n            userId\n        ]);\n    }\n    // Get users with filters and pagination\n    static async getUsersWithFilters(params) {\n        const { page = 1, limit = 20, search, role, vip_level, is_active, is_banned, registration_source, sort_by = 'created_at', sort_order = 'desc' } = params;\n        const offset = (page - 1) * limit;\n        const conditions = [\n            'role = ?'\n        ]; // 只显示普通用户，不包括管理员\n        const values = [\n            'user'\n        ];\n        // Build WHERE conditions\n        if (search) {\n            conditions.push('(username LIKE ? OR email LIKE ? OR full_name LIKE ?)');\n            const searchTerm = `%${search}%`;\n            values.push(searchTerm, searchTerm, searchTerm);\n        }\n        if (role) {\n            conditions.push('role = ?');\n            values.push(role);\n        }\n        if (vip_level) {\n            conditions.push('vip_level = ?');\n            values.push(vip_level);\n        }\n        if (typeof is_active === 'boolean') {\n            conditions.push('is_active = ?');\n            values.push(is_active);\n        }\n        if (typeof is_banned === 'boolean') {\n            conditions.push('is_banned = ?');\n            values.push(is_banned);\n        }\n        if (registration_source) {\n            conditions.push('registration_source = ?');\n            values.push(registration_source);\n        }\n        const whereClause = `WHERE ${conditions.join(' AND ')}`;\n        const orderClause = `ORDER BY ${sort_by} ${sort_order.toUpperCase()}`;\n        // Get total count\n        const countQuery = `SELECT COUNT(*) as total FROM users ${whereClause}`;\n        const countResult = await _database__WEBPACK_IMPORTED_MODULE_3__.db.queryOne(countQuery, values);\n        const total = countResult?.total || 0;\n        // Get users - use string interpolation for LIMIT/OFFSET to avoid parameter issues\n        const usersQuery = `\n      SELECT id, username, email, full_name, role, avatar_url, is_active, is_banned,\n             ban_reason, ban_expires_at, banned_by, banned_at, vip_level, vip_expires_at,\n             registration_source, phone, gender, birth_date, last_login, login_count,\n             created_at, updated_at\n      FROM users\n      ${whereClause}\n      ${orderClause}\n      LIMIT ${limit} OFFSET ${offset}\n    `;\n        const users = await _database__WEBPACK_IMPORTED_MODULE_3__.db.query(usersQuery, values);\n        return {\n            users,\n            total\n        };\n    }\n    // Get user statistics\n    static async getUserStats() {\n        const stats = await _database__WEBPACK_IMPORTED_MODULE_3__.db.queryOne(`\n      SELECT\n        COUNT(*) as total,\n        COALESCE(SUM(CASE WHEN is_active = true THEN 1 ELSE 0 END), 0) as active,\n        COALESCE(SUM(CASE WHEN is_banned = true THEN 1 ELSE 0 END), 0) as banned,\n        COALESCE(SUM(CASE WHEN vip_level != 'none' THEN 1 ELSE 0 END), 0) as vip,\n        COALESCE(SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH) THEN 1 ELSE 0 END), 0) as newThisMonth\n      FROM users\n      WHERE role = 'user'\n    `);\n        return stats ? {\n            total: Number(stats.total) || 0,\n            active: Number(stats.active) || 0,\n            banned: Number(stats.banned) || 0,\n            vip: Number(stats.vip) || 0,\n            newThisMonth: Number(stats.newThisMonth) || 0\n        } : {\n            total: 0,\n            active: 0,\n            banned: 0,\n            vip: 0,\n            newThisMonth: 0\n        };\n    }\n    static async updateUser(id, updates) {\n        const setClause = Object.keys(updates).map((key)=>`${key} = ?`).join(', ');\n        const values = Object.values(updates);\n        await _database__WEBPACK_IMPORTED_MODULE_3__.db.query(`UPDATE users SET ${setClause}, updated_at = NOW() WHERE id = ?`, [\n            ...values,\n            id\n        ]);\n        return this.getUserById(id);\n    }\n    static async deleteUser(id) {\n        await _database__WEBPACK_IMPORTED_MODULE_3__.db.query('DELETE FROM users WHERE id = ?', [\n            id\n        ]);\n    }\n    static async getAllUsers(page = 1, limit = 10) {\n        const offset = (page - 1) * limit;\n        const users = await _database__WEBPACK_IMPORTED_MODULE_3__.db.query(`SELECT id, username, email, full_name, role, avatar_url, is_active, last_login, created_at, updated_at \n       FROM users ORDER BY created_at DESC LIMIT ? OFFSET ?`, [\n            limit,\n            offset\n        ]);\n        const [{ total }] = await _database__WEBPACK_IMPORTED_MODULE_3__.db.query('SELECT COUNT(*) as total FROM users');\n        return {\n            users,\n            total\n        };\n    }\n}\n// Authentication middleware helper\nasync function verifyAuth(token) {\n    try {\n        const payload = JWTUtils.verify(token);\n        if (!payload) {\n            return null;\n        }\n        const [user, session] = await Promise.all([\n            UserManager.getUserById(payload.userId),\n            SessionManager.getSession(payload.sessionId)\n        ]);\n        if (!user || !session || !user.is_active) {\n            return null;\n        }\n        return {\n            user,\n            session\n        };\n    } catch (error) {\n        return null;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/client-detection.ts":
/*!*************************************!*\
  !*** ./src/lib/client-detection.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CLIENT_PERMISSIONS: () => (/* binding */ CLIENT_PERMISSIONS),\n/* harmony export */   ClientDetector: () => (/* binding */ ClientDetector),\n/* harmony export */   ClientPermissionChecker: () => (/* binding */ ClientPermissionChecker),\n/* harmony export */   ClientType: () => (/* binding */ ClientType),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   withClientDetection: () => (/* binding */ withClientDetection),\n/* harmony export */   withClientPermission: () => (/* binding */ withClientPermission)\n/* harmony export */ });\n/**\n * 客户端检测中间件\n * 用于区分不同的客户端类型（admin-system, uniapp等）\n */ // 客户端类型枚举\nvar ClientType = /*#__PURE__*/ function(ClientType) {\n    ClientType[\"ADMIN_WEB\"] = \"admin-web\";\n    ClientType[\"UNIAPP\"] = \"uniapp\";\n    ClientType[\"MOBILE_APP\"] = \"mobile-app\";\n    ClientType[\"THIRD_PARTY\"] = \"third-party\";\n    ClientType[\"UNKNOWN\"] = \"unknown\";\n    return ClientType;\n}({});\n// 客户端检测器\nclass ClientDetector {\n    /**\n   * 检测客户端类型\n   */ static detectClientType(request) {\n        const userAgent = request.headers.get('user-agent') || '';\n        const clientType = request.headers.get('x-client-type');\n        const clientVersion = request.headers.get('x-client-version');\n        // 优先使用自定义header\n        if (clientType) {\n            switch(clientType.toLowerCase()){\n                case 'admin-web':\n                case 'admin-system':\n                    return \"admin-web\";\n                case 'uniapp':\n                case 'uni-app':\n                    return \"uniapp\";\n                case 'mobile-app':\n                    return \"mobile-app\";\n                case 'third-party':\n                    return \"third-party\";\n                default:\n                    break;\n            }\n        }\n        // 基于User-Agent检测\n        if (userAgent.includes('uni-app')) {\n            return \"uniapp\";\n        }\n        if (userAgent.includes('AdminSystem')) {\n            return \"admin-web\";\n        }\n        // 检测移动端\n        if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {\n            return \"mobile-app\";\n        }\n        // 检测浏览器\n        if (/Mozilla|Chrome|Safari|Firefox/.test(userAgent)) {\n            return \"admin-web\";\n        }\n        return \"unknown\";\n    }\n    /**\n   * 获取客户端信息\n   */ static getClientInfo(request) {\n        const userAgent = request.headers.get('user-agent') || '';\n        const clientVersion = request.headers.get('x-client-version');\n        const clientPlatform = request.headers.get('x-client-platform');\n        return {\n            type: this.detectClientType(request),\n            version: clientVersion || undefined,\n            platform: clientPlatform || this.detectPlatform(userAgent),\n            userAgent,\n            ipAddress: this.getClientIP(request)\n        };\n    }\n    /**\n   * 检测平台\n   */ static detectPlatform(userAgent) {\n        if (/Windows/.test(userAgent)) return 'windows';\n        if (/Mac/.test(userAgent)) return 'macos';\n        if (/Linux/.test(userAgent)) return 'linux';\n        if (/Android/.test(userAgent)) return 'android';\n        if (/iPhone|iPad/.test(userAgent)) return 'ios';\n        return 'unknown';\n    }\n    /**\n   * 获取客户端IP地址\n   */ static getClientIP(request) {\n        const forwarded = request.headers.get('x-forwarded-for');\n        const realIP = request.headers.get('x-real-ip');\n        if (forwarded) {\n            return forwarded.split(',')[0].trim();\n        }\n        if (realIP) {\n            return realIP;\n        }\n        return 'unknown';\n    }\n    /**\n   * 检查是否为管理端客户端\n   */ static isAdminClient(clientType) {\n        return clientType === \"admin-web\";\n    }\n    /**\n   * 检查是否为移动端客户端\n   */ static isMobileClient(clientType) {\n        return [\n            \"uniapp\",\n            \"mobile-app\"\n        ].includes(clientType);\n    }\n    /**\n   * 检查是否为可信客户端\n   */ static isTrustedClient(clientType) {\n        return [\n            \"admin-web\",\n            \"uniapp\",\n            \"mobile-app\"\n        ].includes(clientType);\n    }\n}\n// 客户端权限配置\nconst CLIENT_PERMISSIONS = {\n    [\"admin-web\"]: {\n        canAccessAdminAPI: true,\n        canAccessUserAPI: true,\n        canManageUsers: true,\n        canViewAnalytics: true,\n        canExportData: true,\n        rateLimit: {\n            requests: 1000,\n            window: 3600000\n        }\n    },\n    [\"uniapp\"]: {\n        canAccessAdminAPI: false,\n        canAccessUserAPI: true,\n        canManageUsers: false,\n        canViewAnalytics: false,\n        canExportData: false,\n        rateLimit: {\n            requests: 500,\n            window: 3600000\n        }\n    },\n    [\"mobile-app\"]: {\n        canAccessAdminAPI: false,\n        canAccessUserAPI: true,\n        canManageUsers: false,\n        canViewAnalytics: false,\n        canExportData: false,\n        rateLimit: {\n            requests: 300,\n            window: 3600000\n        }\n    },\n    [\"third-party\"]: {\n        canAccessAdminAPI: false,\n        canAccessUserAPI: true,\n        canManageUsers: false,\n        canViewAnalytics: false,\n        canExportData: false,\n        rateLimit: {\n            requests: 100,\n            window: 3600000\n        }\n    },\n    [\"unknown\"]: {\n        canAccessAdminAPI: false,\n        canAccessUserAPI: false,\n        canManageUsers: false,\n        canViewAnalytics: false,\n        canExportData: false,\n        rateLimit: {\n            requests: 50,\n            window: 3600000\n        }\n    }\n};\n// 客户端权限检查器\nclass ClientPermissionChecker {\n    /**\n   * 检查客户端是否有特定权限\n   */ static hasPermission(clientType, permission) {\n        const permissions = CLIENT_PERMISSIONS[clientType];\n        return permissions ? permissions[permission] : false;\n    }\n    /**\n   * 获取客户端限流配置\n   */ static getRateLimit(clientType) {\n        const permissions = CLIENT_PERMISSIONS[clientType];\n        return permissions?.rateLimit || CLIENT_PERMISSIONS[\"unknown\"].rateLimit;\n    }\n    /**\n   * 检查是否可以访问管理API\n   */ static canAccessAdminAPI(clientType) {\n        return this.hasPermission(clientType, 'canAccessAdminAPI');\n    }\n    /**\n   * 检查是否可以访问用户API\n   */ static canAccessUserAPI(clientType) {\n        return this.hasPermission(clientType, 'canAccessUserAPI');\n    }\n    /**\n   * 检查是否可以管理用户\n   */ static canManageUsers(clientType) {\n        return this.hasPermission(clientType, 'canManageUsers');\n    }\n    /**\n   * 检查是否可以查看分析数据\n   */ static canViewAnalytics(clientType) {\n        return this.hasPermission(clientType, 'canViewAnalytics');\n    }\n    /**\n   * 检查是否可以导出数据\n   */ static canExportData(clientType) {\n        return this.hasPermission(clientType, 'canExportData');\n    }\n}\n// 客户端中间件\nfunction withClientDetection(handler) {\n    return async (request)=>{\n        const clientInfo = ClientDetector.getClientInfo(request);\n        // 记录客户端信息（可选）\n        console.log('Client detected:', {\n            type: clientInfo.type,\n            version: clientInfo.version,\n            platform: clientInfo.platform,\n            ip: clientInfo.ipAddress\n        });\n        return handler(request, clientInfo);\n    };\n}\n// 客户端权限中间件\nfunction withClientPermission(permission, handler) {\n    return withClientDetection(async (request, clientInfo)=>{\n        if (!ClientPermissionChecker.hasPermission(clientInfo.type, permission)) {\n            return new Response(JSON.stringify({\n                success: false,\n                error: '客户端权限不足',\n                code: 'INSUFFICIENT_CLIENT_PERMISSION'\n            }), {\n                status: 403,\n                headers: {\n                    'Content-Type': 'application/json'\n                }\n            });\n        }\n        return handler(request, clientInfo);\n    });\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ClientDetector);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/client-detection.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Database: () => (/* binding */ Database),\n/* harmony export */   db: () => (/* binding */ db),\n/* harmony export */   initializeDatabase: () => (/* binding */ initializeDatabase),\n/* harmony export */   runMigrations: () => (/* binding */ runMigrations)\n/* harmony export */ });\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mysql2/promise */ \"mysql2/promise\");\n/* harmony import */ var mysql2_promise__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(mysql2_promise__WEBPACK_IMPORTED_MODULE_0__);\n\n// Database configuration\nconst dbConfig = {\n    host: process.env.DB_HOST || 'localhost',\n    port: parseInt(process.env.DB_PORT || '3306'),\n    user: process.env.DB_USER || 'root',\n    password: process.env.DB_PASSWORD || '',\n    database: process.env.DB_NAME || 'admin_system',\n    waitForConnections: true,\n    connectionLimit: 10,\n    maxIdle: 10,\n    idleTimeout: 60000,\n    queueLimit: 0,\n    enableKeepAlive: true,\n    keepAliveInitialDelay: 0\n};\n// Create connection pool\nconst pool = mysql2_promise__WEBPACK_IMPORTED_MODULE_0___default().createPool(dbConfig);\n// Database connection wrapper\nclass Database {\n    constructor(){\n        this.pool = pool;\n    }\n    static getInstance() {\n        if (!Database.instance) {\n            Database.instance = new Database();\n        }\n        return Database.instance;\n    }\n    // Execute query with parameters\n    async query(sql, params) {\n        try {\n            const [rows] = await this.pool.execute(sql, params);\n            return rows;\n        } catch (error) {\n            console.error('Database query error:', error);\n            throw error;\n        }\n    }\n    // Execute query and return first result\n    async queryOne(sql, params) {\n        const results = await this.query(sql, params);\n        return results.length > 0 ? results[0] : null;\n    }\n    // Execute transaction\n    async transaction(callback) {\n        const connection = await this.pool.getConnection();\n        try {\n            await connection.beginTransaction();\n            const result = await callback(connection);\n            await connection.commit();\n            return result;\n        } catch (error) {\n            await connection.rollback();\n            throw error;\n        } finally{\n            connection.release();\n        }\n    }\n    // Close all connections\n    async close() {\n        await this.pool.end();\n    }\n}\n// Export singleton instance\nconst db = Database.getInstance();\n// Database initialization script\nasync function initializeDatabase() {\n    try {\n        // Create users table\n        await db.query(`\n      CREATE TABLE IF NOT EXISTS users (\n        id INT AUTO_INCREMENT PRIMARY KEY,\n        username VARCHAR(50) UNIQUE NOT NULL,\n        email VARCHAR(100) UNIQUE NOT NULL,\n        password_hash VARCHAR(255) NOT NULL,\n        full_name VARCHAR(100),\n        role ENUM('admin', 'user', 'moderator') DEFAULT 'user',\n        avatar_url VARCHAR(255),\n        is_active BOOLEAN DEFAULT true,\n        is_banned BOOLEAN DEFAULT false,\n        ban_reason TEXT NULL,\n        ban_expires_at TIMESTAMP NULL,\n        banned_by INT NULL,\n        banned_at TIMESTAMP NULL,\n        vip_level ENUM('none', 'bronze', 'silver', 'gold', 'platinum', 'diamond') DEFAULT 'none',\n        vip_expires_at TIMESTAMP NULL,\n        registration_source ENUM('web', 'mobile', 'api', 'admin') DEFAULT 'web',\n        phone VARCHAR(20) NULL,\n        gender ENUM('male', 'female', 'other') NULL,\n        birth_date DATE NULL,\n        last_login TIMESTAMP NULL,\n        login_count INT DEFAULT 0,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,\n        FOREIGN KEY (banned_by) REFERENCES users(id) ON DELETE SET NULL\n      )\n    `);\n        // Create sessions table\n        await db.query(`\n      CREATE TABLE IF NOT EXISTS sessions (\n        id VARCHAR(255) PRIMARY KEY,\n        user_id INT NOT NULL,\n        expires_at TIMESTAMP NOT NULL,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE\n      )\n    `);\n        // Create audit_logs table\n        await db.query(`\n      CREATE TABLE IF NOT EXISTS audit_logs (\n        id INT AUTO_INCREMENT PRIMARY KEY,\n        user_id INT,\n        action VARCHAR(100) NOT NULL,\n        resource VARCHAR(100),\n        resource_id VARCHAR(100),\n        details JSON,\n        ip_address VARCHAR(45),\n        user_agent TEXT,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL\n      )\n    `);\n        // Create system_settings table\n        await db.query(`\n      CREATE TABLE IF NOT EXISTS system_settings (\n        id INT AUTO_INCREMENT PRIMARY KEY,\n        setting_key VARCHAR(100) UNIQUE NOT NULL,\n        setting_value TEXT,\n        description TEXT,\n        is_public BOOLEAN DEFAULT false,\n        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n      )\n    `);\n        // Run database migrations\n        await runMigrations();\n        console.log('Database tables initialized successfully');\n    } catch (error) {\n        console.error('Database initialization error:', error);\n        throw error;\n    }\n}\n// Database migrations\nasync function runMigrations() {\n    try {\n        // Check if migrations table exists\n        await db.query(`\n      CREATE TABLE IF NOT EXISTS migrations (\n        id INT AUTO_INCREMENT PRIMARY KEY,\n        migration_name VARCHAR(255) UNIQUE NOT NULL,\n        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n      )\n    `);\n        // Migration 1: Add new user fields\n        const migration1 = 'add_user_extended_fields_v1';\n        const existingMigration1 = await db.queryOne('SELECT * FROM migrations WHERE migration_name = ?', [\n            migration1\n        ]);\n        if (!existingMigration1) {\n            // Add new columns to users table\n            const alterQueries = [\n                'ALTER TABLE users ADD COLUMN IF NOT EXISTS is_banned BOOLEAN DEFAULT false',\n                'ALTER TABLE users ADD COLUMN IF NOT EXISTS ban_reason TEXT NULL',\n                'ALTER TABLE users ADD COLUMN IF NOT EXISTS ban_expires_at TIMESTAMP NULL',\n                'ALTER TABLE users ADD COLUMN IF NOT EXISTS banned_by INT NULL',\n                'ALTER TABLE users ADD COLUMN IF NOT EXISTS banned_at TIMESTAMP NULL',\n                'ALTER TABLE users ADD COLUMN IF NOT EXISTS vip_level ENUM(\"none\", \"bronze\", \"silver\", \"gold\", \"platinum\", \"diamond\") DEFAULT \"none\"',\n                'ALTER TABLE users ADD COLUMN IF NOT EXISTS vip_expires_at TIMESTAMP NULL',\n                'ALTER TABLE users ADD COLUMN IF NOT EXISTS registration_source ENUM(\"web\", \"mobile\", \"api\", \"admin\") DEFAULT \"web\"',\n                'ALTER TABLE users ADD COLUMN IF NOT EXISTS phone VARCHAR(20) NULL',\n                'ALTER TABLE users ADD COLUMN IF NOT EXISTS gender ENUM(\"male\", \"female\", \"other\") NULL',\n                'ALTER TABLE users ADD COLUMN IF NOT EXISTS birth_date DATE NULL',\n                'ALTER TABLE users ADD COLUMN IF NOT EXISTS login_count INT DEFAULT 0'\n            ];\n            for (const query of alterQueries){\n                try {\n                    await db.query(query);\n                } catch (error) {\n                    // Ignore duplicate column errors\n                    if (!error.message.includes('Duplicate column name')) {\n                        throw error;\n                    }\n                }\n            }\n            // Add foreign key constraint if it doesn't exist\n            try {\n                await db.query(`\n          ALTER TABLE users\n          ADD CONSTRAINT fk_users_banned_by\n          FOREIGN KEY (banned_by) REFERENCES users(id) ON DELETE SET NULL\n        `);\n            } catch (error) {\n                // Ignore if constraint already exists\n                if (!error.message.includes('Duplicate foreign key constraint')) {\n                    console.warn('Could not add foreign key constraint:', error.message);\n                }\n            }\n            // Record migration\n            await db.query('INSERT INTO migrations (migration_name) VALUES (?)', [\n                migration1\n            ]);\n            console.log('Migration completed:', migration1);\n        }\n        console.log('All migrations completed successfully');\n    } catch (error) {\n        console.error('Migration error:', error);\n        throw error;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/database.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!*********************************************************************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \*********************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "./work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "mysql2/promise":
/*!*********************************!*\
  !*** external "mysql2/promise" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("mysql2/promise");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "next/dist/shared/lib/no-fallback-error.external":
/*!******************************************************************!*\
  !*** external "next/dist/shared/lib/no-fallback-error.external" ***!
  \******************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/no-fallback-error.external");

/***/ }),

/***/ "next/dist/shared/lib/router/utils/app-paths":
/*!**************************************************************!*\
  !*** external "next/dist/shared/lib/router/utils/app-paths" ***!
  \**************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/shared/lib/router/utils/app-paths");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0","vendor-chunks/crypto-js@4.2.0","vendor-chunks/semver@7.7.2","vendor-chunks/bcryptjs@3.0.2","vendor-chunks/jsonwebtoken@9.0.2","vendor-chunks/lodash.includes@4.3.0","vendor-chunks/jws@3.2.2","vendor-chunks/lodash.once@4.1.1","vendor-chunks/jwa@1.4.2","vendor-chunks/lodash.isinteger@4.0.4","vendor-chunks/ecdsa-sig-formatter@1.0.11","vendor-chunks/lodash.isplainobject@4.0.6","vendor-chunks/ms@2.1.3","vendor-chunks/lodash.isstring@4.0.1","vendor-chunks/lodash.isnumber@3.0.3","vendor-chunks/lodash.isboolean@3.0.3","vendor-chunks/safe-buffer@5.2.1","vendor-chunks/buffer-equal-constant-time@1.0.1"], () => (__webpack_exec__("(rsc)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fusers%2Froute&page=%2Fapi%2Fusers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fusers%2Froute.ts&appDir=E%3A%5C7mouthMission%5CuniFigma%5Cadmin-system%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5C7mouthMission%5CuniFigma%5Cadmin-system&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D&isGlobalNotFoundEnabled=!")));
module.exports = __webpack_exports__;

})();