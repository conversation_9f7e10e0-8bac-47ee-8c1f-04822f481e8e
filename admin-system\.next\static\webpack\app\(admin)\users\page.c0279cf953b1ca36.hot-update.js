"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(admin)/users/page",{

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/copy.js":
/*!***************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/copy.js ***!
  \***************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Copy)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"rect\",\n        {\n            width: \"14\",\n            height: \"14\",\n            x: \"8\",\n            y: \"8\",\n            rx: \"2\",\n            ry: \"2\",\n            key: \"17jyea\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2\",\n            key: \"zix9uf\"\n        }\n    ]\n];\nconst Copy = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"copy\", __iconNode);\n //# sourceMappingURL=copy.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/copy.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/ticket.js":
/*!*****************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/ticket.js ***!
  \*****************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: () => (/* binding */ __iconNode),\n/* harmony export */   \"default\": () => (/* binding */ Ticket)\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.525.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"path\",\n        {\n            d: \"M2 9a3 3 0 0 1 0 6v2a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2v-2a3 3 0 0 1 0-6V7a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2Z\",\n            key: \"qn84l0\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M13 5v2\",\n            key: \"dyzc3o\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M13 17v2\",\n            key: \"1ont0d\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M13 11v2\",\n            key: \"1wjjxi\"\n        }\n    ]\n];\nconst Ticket = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"ticket\", __iconNode);\n //# sourceMappingURL=ticket.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy8ucG5wbS9sdWNpZGUtcmVhY3RAMC41MjUuMF9yZWFjdEAxOS4xLjAvbm9kZV9tb2R1bGVzL2x1Y2lkZS1yZWFjdC9kaXN0L2VzbS9pY29ucy90aWNrZXQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBR08sTUFBTSxVQUF1QjtJQUNsQztRQUNFO1FBQ0E7WUFDRSxDQUFHO1lBQ0gsR0FBSztRQUFBO0tBRVQ7SUFDQTtRQUFDLE1BQVE7UUFBQTtZQUFFLEdBQUcsQ0FBVztZQUFBLEtBQUs7UUFBQSxDQUFVO0tBQUE7SUFDeEM7UUFBQyxNQUFRO1FBQUE7WUFBRSxHQUFHLENBQVk7WUFBQSxLQUFLO1FBQUEsQ0FBVTtLQUFBO0lBQ3pDO1FBQUMsQ0FBUTtRQUFBLENBQUU7WUFBQSxFQUFHLFdBQVk7WUFBQSxJQUFLO1FBQVU7S0FBQTtDQUMzQztBQWFNLGFBQVMsa0VBQWlCLFdBQVUsQ0FBVSIsInNvdXJjZXMiOlsiRTpcXHNyY1xcaWNvbnNcXHRpY2tldC50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3JlYXRlTHVjaWRlSWNvbiBmcm9tICcuLi9jcmVhdGVMdWNpZGVJY29uJztcbmltcG9ydCB7IEljb25Ob2RlIH0gZnJvbSAnLi4vdHlwZXMnO1xuXG5leHBvcnQgY29uc3QgX19pY29uTm9kZTogSWNvbk5vZGUgPSBbXG4gIFtcbiAgICAncGF0aCcsXG4gICAge1xuICAgICAgZDogJ00yIDlhMyAzIDAgMCAxIDAgNnYyYTIgMiAwIDAgMCAyIDJoMTZhMiAyIDAgMCAwIDItMnYtMmEzIDMgMCAwIDEgMC02VjdhMiAyIDAgMCAwLTItMkg0YTIgMiAwIDAgMC0yIDJaJyxcbiAgICAgIGtleTogJ3FuODRsMCcsXG4gICAgfSxcbiAgXSxcbiAgWydwYXRoJywgeyBkOiAnTTEzIDV2MicsIGtleTogJ2R5emMzbycgfV0sXG4gIFsncGF0aCcsIHsgZDogJ00xMyAxN3YyJywga2V5OiAnMW9udDBkJyB9XSxcbiAgWydwYXRoJywgeyBkOiAnTTEzIDExdjInLCBrZXk6ICcxd2pqeGknIH1dLFxuXTtcblxuLyoqXG4gKiBAY29tcG9uZW50IEBuYW1lIFRpY2tldFxuICogQGRlc2NyaXB0aW9uIEx1Y2lkZSBTVkcgaWNvbiBjb21wb25lbnQsIHJlbmRlcnMgU1ZHIEVsZW1lbnQgd2l0aCBjaGlsZHJlbi5cbiAqXG4gKiBAcHJldmlldyAhW2ltZ10oZGF0YTppbWFnZS9zdmcreG1sO2Jhc2U2NCxQSE4yWnlBZ2VHMXNibk05SW1oMGRIQTZMeTkzZDNjdWR6TXViM0puTHpJd01EQXZjM1puSWdvZ0lIZHBaSFJvUFNJeU5DSUtJQ0JvWldsbmFIUTlJakkwSWdvZ0lIWnBaWGRDYjNnOUlqQWdNQ0F5TkNBeU5DSUtJQ0JtYVd4c1BTSnViMjVsSWdvZ0lITjBjbTlyWlQwaUl6QXdNQ0lnYzNSNWJHVTlJbUpoWTJ0bmNtOTFibVF0WTI5c2IzSTZJQ05tWm1ZN0lHSnZjbVJsY2kxeVlXUnBkWE02SURKd2VDSUtJQ0J6ZEhKdmEyVXRkMmxrZEdnOUlqSWlDaUFnYzNSeWIydGxMV3hwYm1WallYQTlJbkp2ZFc1a0lnb2dJSE4wY205clpTMXNhVzVsYW05cGJqMGljbTkxYm1RaUNqNEtJQ0E4Y0dGMGFDQmtQU0pOTWlBNVlUTWdNeUF3SURBZ01TQXdJRFoyTW1FeUlESWdNQ0F3SURBZ01pQXlhREUyWVRJZ01pQXdJREFnTUNBeUxUSjJMVEpoTXlBeklEQWdNQ0F4SURBdE5sWTNZVElnTWlBd0lEQWdNQzB5TFRKSU5HRXlJRElnTUNBd0lEQXRNaUF5V2lJZ0x6NEtJQ0E4Y0dGMGFDQmtQU0pOTVRNZ05YWXlJaUF2UGdvZ0lEeHdZWFJvSUdROUlrMHhNeUF4TjNZeUlpQXZQZ29nSUR4d1lYUm9JR1E5SWsweE15QXhNWFl5SWlBdlBnbzhMM04yWno0SykgLSBodHRwczovL2x1Y2lkZS5kZXYvaWNvbnMvdGlja2V0XG4gKiBAc2VlIGh0dHBzOi8vbHVjaWRlLmRldi9ndWlkZS9wYWNrYWdlcy9sdWNpZGUtcmVhY3QgLSBEb2N1bWVudGF0aW9uXG4gKlxuICogQHBhcmFtIHtPYmplY3R9IHByb3BzIC0gTHVjaWRlIGljb25zIHByb3BzIGFuZCBhbnkgdmFsaWQgU1ZHIGF0dHJpYnV0ZVxuICogQHJldHVybnMge0pTWC5FbGVtZW50fSBKU1ggRWxlbWVudFxuICpcbiAqL1xuY29uc3QgVGlja2V0ID0gY3JlYXRlTHVjaWRlSWNvbigndGlja2V0JywgX19pY29uTm9kZSk7XG5cbmV4cG9ydCBkZWZhdWx0IFRpY2tldDtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/ticket.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/(admin)/users/page.tsx":
/*!****************************************!*\
  !*** ./src/app/(admin)/users/page.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Plus,RefreshCw!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_api_helpers__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api-helpers */ \"(app-pages-browser)/./src/lib/api-helpers.ts\");\n/* harmony import */ var _components_users_user_stats_cards__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/users/user-stats-cards */ \"(app-pages-browser)/./src/components/users/user-stats-cards.tsx\");\n/* harmony import */ var _components_users_user_filters__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/users/user-filters */ \"(app-pages-browser)/./src/components/users/user-filters.tsx\");\n/* harmony import */ var _components_users_user_table__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/users/user-table */ \"(app-pages-browser)/./src/components/users/user-table.tsx\");\n/* harmony import */ var _components_users_user_pagination__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/users/user-pagination */ \"(app-pages-browser)/./src/components/users/user-pagination.tsx\");\n/* harmony import */ var _components_users_user_action_dialogs__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/users/user-action-dialogs */ \"(app-pages-browser)/./src/components/users/user-action-dialogs.tsx\");\n/* harmony import */ var _components_users_user_activation_history__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/users/user-activation-history */ \"(app-pages-browser)/./src/components/users/user-activation-history.tsx\");\n/* harmony import */ var _services_user_service__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/services/user.service */ \"(app-pages-browser)/./src/services/user.service.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n// Import our new components\n\n\n\n\n\n\n// Import services and types\n\nfunction UsersPage() {\n    _s();\n    // State management\n    const [users, setUsers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        total: 0,\n        active: 0,\n        banned: 0,\n        vip: 0,\n        newThisMonth: 0\n    });\n    // Loading states are now managed by LoadingManager\n    const [loadingStates, setLoadingStates] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(_lib_api_helpers__WEBPACK_IMPORTED_MODULE_6__.LoadingManager.getState());\n    // Subscribe to loading state changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UsersPage.useEffect\": ()=>{\n            const unsubscribe = _lib_api_helpers__WEBPACK_IMPORTED_MODULE_6__.LoadingManager.subscribe(setLoadingStates);\n            return unsubscribe;\n        }\n    }[\"UsersPage.useEffect\"], []);\n    // Pagination state\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 20,\n        total: 0,\n        totalPages: 0\n    });\n    // Filter state\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        page: 1,\n        limit: 20\n    });\n    // Dialog state\n    const [banDialog, setBanDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        open: false,\n        user: null\n    });\n    const [vipDialog, setVipDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        open: false,\n        user: null\n    });\n    const [activationHistoryDialog, setActivationHistoryDialog] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        open: false,\n        user: null\n    });\n    // Load users data\n    const loadUsers = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"UsersPage.useCallback[loadUsers]\": async ()=>{\n            const result = await (0,_lib_api_helpers__WEBPACK_IMPORTED_MODULE_6__.apiCall)({\n                \"UsersPage.useCallback[loadUsers]\": ()=>_services_user_service__WEBPACK_IMPORTED_MODULE_13__.UserService.getUsers(filters)\n            }[\"UsersPage.useCallback[loadUsers]\"], {\n                loadingKey: 'loadUsers',\n                showErrorToast: true,\n                errorMessage: '加载用户列表失败'\n            });\n            if (result.success && result.data) {\n                var _result_data_pagination, _result_data_pagination1, _result_data_pagination2, _result_data_pagination3;\n                setUsers(result.data.users);\n                setPagination({\n                    page: ((_result_data_pagination = result.data.pagination) === null || _result_data_pagination === void 0 ? void 0 : _result_data_pagination.page) || 1,\n                    limit: ((_result_data_pagination1 = result.data.pagination) === null || _result_data_pagination1 === void 0 ? void 0 : _result_data_pagination1.limit) || 20,\n                    total: ((_result_data_pagination2 = result.data.pagination) === null || _result_data_pagination2 === void 0 ? void 0 : _result_data_pagination2.total) || 0,\n                    totalPages: ((_result_data_pagination3 = result.data.pagination) === null || _result_data_pagination3 === void 0 ? void 0 : _result_data_pagination3.totalPages) || 0\n                });\n            }\n        }\n    }[\"UsersPage.useCallback[loadUsers]\"], [\n        filters\n    ]);\n    // Load user statistics\n    const loadStats = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"UsersPage.useCallback[loadStats]\": async ()=>{\n            const result = await (0,_lib_api_helpers__WEBPACK_IMPORTED_MODULE_6__.apiCall)({\n                \"UsersPage.useCallback[loadStats]\": ()=>_services_user_service__WEBPACK_IMPORTED_MODULE_13__.UserService.getUserStats()\n            }[\"UsersPage.useCallback[loadStats]\"], {\n                loadingKey: 'loadStats',\n                showErrorToast: true,\n                errorMessage: '加载统计数据失败'\n            });\n            if (result.success && result.data) {\n                setStats(result.data);\n            }\n        }\n    }[\"UsersPage.useCallback[loadStats]\"], []);\n    // Event handlers\n    const handleFiltersChange = (newFilters)=>{\n        setFilters(newFilters);\n    };\n    const handleFiltersReset = ()=>{\n        setFilters({\n            page: 1,\n            limit: 20\n        });\n    };\n    const handlePageChange = (page)=>{\n        setFilters((prev)=>({\n                ...prev,\n                page\n            }));\n    };\n    const handlePageSizeChange = (limit)=>{\n        setFilters((prev)=>({\n                ...prev,\n                limit,\n                page: 1\n            }));\n    };\n    const handleRefresh = ()=>{\n        loadUsers();\n        loadStats();\n    };\n    // User action handlers\n    const handleBanUser = async (reason, expiresAt)=>{\n        if (!banDialog.user) return;\n        const result = await (0,_lib_api_helpers__WEBPACK_IMPORTED_MODULE_6__.apiCall)(()=>_services_user_service__WEBPACK_IMPORTED_MODULE_13__.UserService.banUser(banDialog.user.id, {\n                reason,\n                expiresAt: expiresAt === null || expiresAt === void 0 ? void 0 : expiresAt.toISOString()\n            }), {\n            loadingKey: 'banUser',\n            showSuccessToast: true,\n            successMessage: '用户已封禁',\n            showErrorToast: true,\n            errorMessage: '封禁用户失败'\n        });\n        if (result.success) {\n            setBanDialog({\n                open: false,\n                user: null\n            });\n            loadUsers();\n            loadStats();\n        }\n    };\n    const handleUnbanUser = async (user)=>{\n        const result = await (0,_lib_api_helpers__WEBPACK_IMPORTED_MODULE_6__.apiCall)(()=>_services_user_service__WEBPACK_IMPORTED_MODULE_13__.UserService.unbanUser(user.id), {\n            loadingKey: 'unbanUser',\n            showSuccessToast: true,\n            successMessage: '用户已解封',\n            showErrorToast: true,\n            errorMessage: '解封用户失败'\n        });\n        if (result.success) {\n            loadUsers();\n            loadStats();\n        }\n    };\n    const handleSetVip = async (level, expiresAt)=>{\n        if (!vipDialog.user) return;\n        const result = await (0,_lib_api_helpers__WEBPACK_IMPORTED_MODULE_6__.apiCall)(()=>_services_user_service__WEBPACK_IMPORTED_MODULE_13__.UserService.setVipLevel(vipDialog.user.id, {\n                level: level,\n                expiresAt: expiresAt === null || expiresAt === void 0 ? void 0 : expiresAt.toISOString()\n            }), {\n            loadingKey: 'setVip',\n            showSuccessToast: true,\n            successMessage: 'VIP等级已设置',\n            showErrorToast: true,\n            errorMessage: '设置VIP等级失败'\n        });\n        if (result.success) {\n            setVipDialog({\n                open: false,\n                user: null\n            });\n            loadUsers();\n            loadStats();\n        }\n    };\n    const handleViewUser = (_user)=>{\n        // TODO: Implement user detail view\n        sonner__WEBPACK_IMPORTED_MODULE_5__.toast.info('用户详情功能开发中');\n    };\n    const handleEditUser = (_user)=>{\n        // TODO: Implement user edit\n        sonner__WEBPACK_IMPORTED_MODULE_5__.toast.info('编辑用户功能开发中');\n    };\n    const handleViewActivationHistory = (user)=>{\n        setActivationHistoryDialog({\n            open: true,\n            user\n        });\n    };\n    // Load data on mount and when filters change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UsersPage.useEffect\": ()=>{\n            loadUsers();\n        }\n    }[\"UsersPage.useEffect\"], [\n        loadUsers\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UsersPage.useEffect\": ()=>{\n            loadStats();\n        }\n    }[\"UsersPage.useEffect\"], [\n        loadStats\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 space-y-6 p-4 md:p-8 pt-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-3xl font-bold tracking-tight\",\n                                children: \"用户管理\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: \"管理uniapp前端注册用户，包括搜索、封号解封、VIP等级管理等功能\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                                lineNumber: 228,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"outline\",\n                                onClick: handleRefresh,\n                                disabled: loadingStates.loadUsers || loadingStates.loadStats,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4 \".concat(loadingStates.loadUsers || loadingStates.loadStats ? 'animate-spin' : '')\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"刷新\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Plus_RefreshCw_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"添加用户\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                                lineNumber: 241,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                        lineNumber: 232,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_user_stats_cards__WEBPACK_IMPORTED_MODULE_7__.UserStatsCards, {\n                stats: stats,\n                loading: loadingStates.loadStats\n            }, void 0, false, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                lineNumber: 249,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                children: \"用户列表\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                                lineNumber: 254,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                children: \"管理所有注册用户，支持搜索、筛选和批量操作\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                                lineNumber: 255,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                        lineNumber: 253,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_user_filters__WEBPACK_IMPORTED_MODULE_8__.UserFilters, {\n                                filters: filters,\n                                onFiltersChange: handleFiltersChange,\n                                onReset: handleFiltersReset\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_user_table__WEBPACK_IMPORTED_MODULE_9__.UserTable, {\n                                users: users,\n                                loading: loadingStates.loadUsers,\n                                onBanUser: (user)=>setBanDialog({\n                                        open: true,\n                                        user\n                                    }),\n                                onUnbanUser: handleUnbanUser,\n                                onSetVip: (user)=>setVipDialog({\n                                        open: true,\n                                        user\n                                    }),\n                                onViewUser: handleViewUser,\n                                onEditUser: handleEditUser,\n                                onViewActivationHistory: handleViewActivationHistory\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                                lineNumber: 268,\n                                columnNumber: 11\n                            }, this),\n                            pagination.total > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_user_pagination__WEBPACK_IMPORTED_MODULE_10__.UserPagination, {\n                                currentPage: pagination.page,\n                                totalPages: pagination.totalPages,\n                                pageSize: pagination.limit,\n                                total: pagination.total,\n                                onPageChange: handlePageChange,\n                                onPageSizeChange: handlePageSizeChange\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                                lineNumber: 281,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                        lineNumber: 259,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                lineNumber: 252,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_user_action_dialogs__WEBPACK_IMPORTED_MODULE_11__.BanUserDialog, {\n                open: banDialog.open,\n                onOpenChange: (open)=>setBanDialog({\n                        open,\n                        user: banDialog.user\n                    }),\n                user: banDialog.user,\n                onConfirm: handleBanUser,\n                loading: loadingStates.banUser\n            }, void 0, false, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                lineNumber: 294,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_user_action_dialogs__WEBPACK_IMPORTED_MODULE_11__.SetVipDialog, {\n                open: vipDialog.open,\n                onOpenChange: (open)=>setVipDialog({\n                        open,\n                        user: vipDialog.user\n                    }),\n                user: vipDialog.user,\n                onConfirm: handleSetVip,\n                loading: loadingStates.setVip\n            }, void 0, false, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                lineNumber: 302,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_users_user_activation_history__WEBPACK_IMPORTED_MODULE_12__.UserActivationHistory, {\n                open: activationHistoryDialog.open,\n                onOpenChange: (open)=>setActivationHistoryDialog({\n                        open,\n                        user: activationHistoryDialog.user\n                    }),\n                user: activationHistoryDialog.user\n            }, void 0, false, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n                lineNumber: 310,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\app\\\\(admin)\\\\users\\\\page.tsx\",\n        lineNumber: 223,\n        columnNumber: 5\n    }, this);\n}\n_s(UsersPage, \"84opPpIGKTu/UnxAdqOtXowGQKg=\");\n_c = UsersPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (_c1 = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.withAuth)(UsersPage));\nvar _c, _c1;\n$RefreshReg$(_c, \"UsersPage\");\n$RefreshReg$(_c1, \"%default%\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/(admin)/users/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/users/user-activation-history.tsx":
/*!**********************************************************!*\
  !*** ./src/components/users/user-activation-history.tsx ***!
  \**********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserActivationHistory: () => (/* binding */ UserActivationHistory)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Copy_Ticket_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Ticket!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/ticket.js\");\n/* harmony import */ var _barrel_optimize_names_Copy_Ticket_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Copy,Ticket!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/zh-CN.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _lib_api_helpers__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/api-helpers */ \"(app-pages-browser)/./src/lib/api-helpers.ts\");\n/* harmony import */ var _services_activation_code_service__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/services/activation-code.service */ \"(app-pages-browser)/./src/services/activation-code.service.ts\");\n/* __next_internal_client_entry_do_not_use__ UserActivationHistory auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\nfunction UserActivationHistory(param) {\n    let { open, onOpenChange, user } = param;\n    var _VIP_LEVEL_CONFIG_codes_reduce, _codes_;\n    _s();\n    const [codes, setCodes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Load user's activation codes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"UserActivationHistory.useEffect\": ()=>{\n            if (open && user) {\n                loadUserActivationCodes();\n            }\n        }\n    }[\"UserActivationHistory.useEffect\"], [\n        open,\n        user\n    ]);\n    const loadUserActivationCodes = async ()=>{\n        if (!user) return;\n        setLoading(true);\n        const result = await (0,_lib_api_helpers__WEBPACK_IMPORTED_MODULE_7__.apiCall)(()=>_services_activation_code_service__WEBPACK_IMPORTED_MODULE_8__.ActivationCodeService.getActivationCodes({\n                page: 1,\n                limit: 100,\n                status: 'used',\n                sort_by: 'used_at',\n                sort_order: 'desc'\n            }), {\n            showErrorToast: true\n        });\n        if (result.success && result.data) {\n            // 过滤出该用户使用的激活码\n            const userCodes = result.data.codes.filter((code)=>code.used_by === user.id);\n            setCodes(userCodes);\n        }\n        setLoading(false);\n    };\n    const handleCopyCode = async (code)=>{\n        try {\n            await navigator.clipboard.writeText(code);\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('激活码已复制到剪贴板');\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('复制失败');\n        }\n    };\n    const getVipLevelBadge = (level)=>{\n        const config = _services_activation_code_service__WEBPACK_IMPORTED_MODULE_8__.VIP_LEVEL_CONFIG[level];\n        if (!config) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                variant: \"outline\",\n                children: level\n            }, void 0, false, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                lineNumber: 88,\n                columnNumber: 14\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n            variant: \"outline\",\n            className: \"gap-1\",\n            style: {\n                borderColor: config.color,\n                color: config.color\n            },\n            children: config.label\n        }, void 0, false, {\n            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n            lineNumber: 92,\n            columnNumber: 7\n        }, this);\n    };\n    const formatDate = (dateString)=>{\n        return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_9__.format)(new Date(dateString), \"PPP\", {\n            locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_10__.zhCN\n        });\n    };\n    if (!user) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.Dialog, {\n        open: open,\n        onOpenChange: onOpenChange,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogContent, {\n            className: \"sm:max-w-[800px]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogHeader, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogTitle, {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Ticket_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this),\n                                \"激活码使用历史\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_2__.DialogDescription, {\n                            children: [\n                                \"查看用户 \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: user.username\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 18\n                                }, this),\n                                \" 的激活码使用记录\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                            lineNumber: 119,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-4\",\n                    children: [\n                        loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-2\",\n                            children: Array.from({\n                                length: 3\n                            }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-12 bg-gray-200 rounded animate-pulse\"\n                                }, index, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                    lineNumber: 128,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                            lineNumber: 126,\n                            columnNumber: 13\n                        }, this) : codes.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center py-8 text-muted-foreground\",\n                            children: \"该用户暂未使用过激活码\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-md border\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.Table, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                    children: \"激活码\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                    children: \"VIP等级\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                                    lineNumber: 141,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                    children: \"有效期\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                                    lineNumber: 142,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                    children: \"使用时间\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                                    children: \"操作\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                                    lineNumber: 144,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableBody, {\n                                        children: codes.map((code)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                            className: \"text-sm font-mono bg-muted px-2 py-1 rounded\",\n                                                            children: _services_activation_code_service__WEBPACK_IMPORTED_MODULE_8__.ActivationCodeService.formatCode(code.code)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                                            lineNumber: 151,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                                        lineNumber: 150,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                        children: getVipLevelBadge(code.vip_level)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                                        lineNumber: 155,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                        className: \"text-sm\",\n                                                        children: [\n                                                            code.vip_duration_days,\n                                                            \" 天\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                        className: \"text-sm\",\n                                                        children: code.used_at ? formatDate(code.used_at) : '-'\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                                        lineNumber: 161,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>handleCopyCode(code.code),\n                                                            className: \"h-8 w-8 p-0\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Copy_Ticket_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                                                lineNumber: 171,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                                            lineNumber: 165,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, code.id, true, {\n                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                                lineNumber: 149,\n                                                columnNumber: 21\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, this),\n                        codes.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-muted/50 rounded-lg p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium mb-2\",\n                                    children: \"使用统计\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"总使用次数\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                                    lineNumber: 187,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium\",\n                                                    children: [\n                                                        codes.length,\n                                                        \" 次\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"总VIP天数\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium\",\n                                                    children: [\n                                                        codes.reduce((sum, code)=>sum + code.vip_duration_days, 0),\n                                                        \" 天\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"最高等级\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                                    lineNumber: 197,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium\",\n                                                    children: codes.length > 0 ? ((_VIP_LEVEL_CONFIG_codes_reduce = _services_activation_code_service__WEBPACK_IMPORTED_MODULE_8__.VIP_LEVEL_CONFIG[codes.reduce((highest, code)=>code.vip_level > highest ? code.vip_level : highest, 'v1')]) === null || _VIP_LEVEL_CONFIG_codes_reduce === void 0 ? void 0 : _VIP_LEVEL_CONFIG_codes_reduce.label) || 'V1' : '-'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                            lineNumber: 196,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-muted-foreground\",\n                                                    children: \"首次使用\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-medium\",\n                                                    children: codes.length > 0 && ((_codes_ = codes[codes.length - 1]) === null || _codes_ === void 0 ? void 0 : _codes_.used_at) ? formatDate(codes[codes.length - 1].used_at) : '-'\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                            lineNumber: 183,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                    lineNumber: 124,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-end\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        variant: \"outline\",\n                        onClick: ()=>onOpenChange(false),\n                        children: \"关闭\"\n                    }, void 0, false, {\n                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                        lineNumber: 223,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n                    lineNumber: 222,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n            lineNumber: 113,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-activation-history.tsx\",\n        lineNumber: 112,\n        columnNumber: 5\n    }, this);\n}\n_s(UserActivationHistory, \"Q9GwXeznJoNqrCfGi47+OoYmD/Q=\");\n_c = UserActivationHistory;\nvar _c;\n$RefreshReg$(_c, \"UserActivationHistory\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/users/user-activation-history.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/users/user-table.tsx":
/*!*********************************************!*\
  !*** ./src/components/users/user-table.tsx ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UserTable: () => (/* binding */ UserTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_Crown_Edit_Eye_MoreHorizontal_Shield_ShieldOff_Ticket_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Edit,Eye,MoreHorizontal,Shield,ShieldOff,Ticket!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_Edit_Eye_MoreHorizontal_Shield_ShieldOff_Ticket_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Edit,Eye,MoreHorizontal,Shield,ShieldOff,Ticket!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_Edit_Eye_MoreHorizontal_Shield_ShieldOff_Ticket_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Edit,Eye,MoreHorizontal,Shield,ShieldOff,Ticket!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_Edit_Eye_MoreHorizontal_Shield_ShieldOff_Ticket_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Edit,Eye,MoreHorizontal,Shield,ShieldOff,Ticket!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_Edit_Eye_MoreHorizontal_Shield_ShieldOff_Ticket_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Edit,Eye,MoreHorizontal,Shield,ShieldOff,Ticket!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/ticket.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_Edit_Eye_MoreHorizontal_Shield_ShieldOff_Ticket_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Edit,Eye,MoreHorizontal,Shield,ShieldOff,Ticket!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/shield-off.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_Edit_Eye_MoreHorizontal_Shield_ShieldOff_Ticket_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,Edit,Eye,MoreHorizontal,Shield,ShieldOff,Ticket!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=format!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/format.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/zh-CN.js\");\n/* __next_internal_client_entry_do_not_use__ UserTable auto */ \n\n\n\n\n\n\n\n\nfunction UserTable(param) {\n    let { users, loading, onBanUser, onUnbanUser, onSetVip, onViewUser, onEditUser, onViewActivationHistory } = param;\n    const getRoleBadge = (role)=>{\n        switch(role){\n            case 'admin':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                    variant: \"destructive\",\n                    children: \"管理员\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 16\n                }, this);\n            case 'moderator':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                    variant: \"default\",\n                    children: \"版主\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                    lineNumber: 64,\n                    columnNumber: 16\n                }, this);\n            case 'user':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                    variant: \"secondary\",\n                    children: \"用户\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                    lineNumber: 66,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                    variant: \"outline\",\n                    children: role\n                }, void 0, false, {\n                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    const getVipBadge = (level)=>{\n        const vipConfig = {\n            none: {\n                label: '普通',\n                variant: 'outline'\n            },\n            bronze: {\n                label: '青铜',\n                variant: 'secondary'\n            },\n            silver: {\n                label: '白银',\n                variant: 'secondary'\n            },\n            gold: {\n                label: 'default',\n                variant: 'default'\n            },\n            platinum: {\n                label: '铂金',\n                variant: 'default'\n            },\n            diamond: {\n                label: '钻石',\n                variant: 'destructive'\n            }\n        };\n        const config = vipConfig[level] || vipConfig.none;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n            variant: config.variant,\n            className: \"gap-1\",\n            children: [\n                level !== 'none' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Edit_Eye_MoreHorizontal_Shield_ShieldOff_Ticket_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"h-3 w-3\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                    lineNumber: 86,\n                    columnNumber: 30\n                }, this),\n                config.label\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n            lineNumber: 85,\n            columnNumber: 7\n        }, this);\n    };\n    const getStatusBadge = (user)=>{\n        if (user.is_banned) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                variant: \"destructive\",\n                children: \"已封禁\"\n            }, void 0, false, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                lineNumber: 94,\n                columnNumber: 14\n            }, this);\n        }\n        if (!user.is_active) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n                variant: \"secondary\",\n                children: \"非活跃\"\n            }, void 0, false, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                lineNumber: 97,\n                columnNumber: 14\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n            variant: \"default\",\n            className: \"bg-green-500\",\n            children: \"正常\"\n        }, void 0, false, {\n            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n            lineNumber: 99,\n            columnNumber: 12\n        }, this);\n    };\n    const getRegistrationSourceBadge = (source)=>{\n        const sourceConfig = {\n            web: {\n                label: '网页',\n                variant: 'outline'\n            },\n            mobile: {\n                label: '移动端',\n                variant: 'secondary'\n            },\n            api: {\n                label: 'API',\n                variant: 'default'\n            },\n            admin: {\n                label: '管理员',\n                variant: 'destructive'\n            }\n        };\n        const config = sourceConfig[source] || sourceConfig.web;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_2__.Badge, {\n            variant: config.variant,\n            children: config.label\n        }, void 0, false, {\n            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n            lineNumber: 111,\n            columnNumber: 12\n        }, this);\n    };\n    const formatDate = (dateString)=>{\n        if (!dateString) return '-';\n        try {\n            return (0,_barrel_optimize_names_format_date_fns__WEBPACK_IMPORTED_MODULE_7__.format)(new Date(dateString), 'yyyy-MM-dd HH:mm', {\n                locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_8__.zhCN\n            });\n        } catch (e) {\n            return '-';\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"space-y-4\",\n            children: Array.from({\n                length: 5\n            }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-4 p-4 border rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-10 w-10 bg-gray-200 rounded-full animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-4 bg-gray-200 rounded animate-pulse w-1/4\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                    lineNumber: 130,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"h-3 bg-gray-200 rounded animate-pulse w-1/3\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                    lineNumber: 131,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"h-8 w-20 bg-gray-200 rounded animate-pulse\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, index, true, {\n                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                    lineNumber: 127,\n                    columnNumber: 11\n                }, this))\n        }, void 0, false, {\n            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"rounded-md border\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.Table, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                children: \"用户\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                children: \"角色\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                children: \"VIP等级\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                children: \"状态\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                children: \"注册来源\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                children: \"登录次数\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                children: \"最后登录\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                children: \"注册时间\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                lineNumber: 152,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableHead, {\n                                className: \"text-right\",\n                                children: \"操作\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                        lineNumber: 144,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                    lineNumber: 143,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableBody, {\n                    children: !users || users.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                            colSpan: 9,\n                            className: \"text-center py-8 text-muted-foreground\",\n                            children: \"暂无用户数据\"\n                        }, void 0, false, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 13\n                    }, this) : users === null || users === void 0 ? void 0 : users.map((user)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableRow, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                    className: \"font-medium\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.Avatar, {\n                                                className: \"h-8 w-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarImage, {\n                                                        src: user.avatar_url || \"https://avatar.vercel.sh/\".concat(user.username)\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                        lineNumber: 169,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_4__.AvatarFallback, {\n                                                        children: user.username.charAt(0).toUpperCase()\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                lineNumber: 168,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"font-medium\",\n                                                        children: user.full_name || user.username\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                        lineNumber: 175,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-muted-foreground\",\n                                                        children: [\n                                                            \"@\",\n                                                            user.username,\n                                                            \" • \",\n                                                            user.email\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                        lineNumber: 176,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    user.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-xs text-muted-foreground\",\n                                                        children: user.phone\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                        lineNumber: 180,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                lineNumber: 174,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                    lineNumber: 166,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                    children: getRoleBadge(user.role)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                    lineNumber: 187,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                    children: getVipBadge(user.vip_level)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                    children: getStatusBadge(user)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                    lineNumber: 189,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                    children: getRegistrationSourceBadge(user.registration_source)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                    className: \"text-sm\",\n                                    children: user.login_count\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: formatDate(user.last_login)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                    className: \"text-sm text-muted-foreground\",\n                                    children: formatDate(user.created_at)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_3__.TableCell, {\n                                    className: \"text-right\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenu, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                                    variant: \"ghost\",\n                                                    className: \"h-8 w-8 p-0\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"sr-only\",\n                                                            children: \"打开菜单\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Edit_Eye_MoreHorizontal_Shield_ShieldOff_Ticket_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                            lineNumber: 203,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuContent, {\n                                                align: \"end\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuLabel, {\n                                                        children: \"操作\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                        onClick: ()=>onViewUser(user),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Edit_Eye_MoreHorizontal_Shield_ShieldOff_Ticket_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                                lineNumber: 209,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"查看详情\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                        onClick: ()=>onEditUser(user),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Edit_Eye_MoreHorizontal_Shield_ShieldOff_Ticket_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                                lineNumber: 213,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"编辑用户\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                        lineNumber: 212,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuSeparator, {}, void 0, false, {\n                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                        onClick: ()=>onSetVip(user),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Edit_Eye_MoreHorizontal_Shield_ShieldOff_Ticket_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                                lineNumber: 218,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"设置VIP\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                        onClick: ()=>onViewActivationHistory(user),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Edit_Eye_MoreHorizontal_Shield_ShieldOff_Ticket_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                                lineNumber: 222,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"激活码历史\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                        lineNumber: 221,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuSeparator, {}, void 0, false, {\n                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                        lineNumber: 225,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    user.is_banned ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                        onClick: ()=>onUnbanUser(user),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Edit_Eye_MoreHorizontal_Shield_ShieldOff_Ticket_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                                lineNumber: 228,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"解除封禁\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                        lineNumber: 227,\n                                                        columnNumber: 25\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                        onClick: ()=>onBanUser(user),\n                                                        className: \"text-red-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_Edit_Eye_MoreHorizontal_Shield_ShieldOff_Ticket_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                                lineNumber: 236,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            \"封禁用户\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                        lineNumber: 232,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, user.id, true, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n                    lineNumber: 156,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n            lineNumber: 142,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\users\\\\user-table.tsx\",\n        lineNumber: 141,\n        columnNumber: 5\n    }, this);\n}\n_c = UserTable;\nvar _c;\n$RefreshReg$(_c, \"UserTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/users/user-table.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/services/activation-code.service.ts":
/*!*************************************************!*\
  !*** ./src/services/activation-code.service.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ActivationCodeService: () => (/* binding */ ActivationCodeService),\n/* harmony export */   VIP_LEVEL_CONFIG: () => (/* binding */ VIP_LEVEL_CONFIG),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/**\n * 激活码管理服务\n * 处理激活码管理相关的API调用\n */ \n// VIP等级配置\nconst VIP_LEVEL_CONFIG = {\n    v1: {\n        label: 'VIP V1',\n        description: '基础VIP会员',\n        default_duration: 30,\n        color: '#10B981'\n    },\n    v2: {\n        label: 'VIP V2',\n        description: '进阶VIP会员',\n        default_duration: 60,\n        color: '#3B82F6'\n    },\n    v3: {\n        label: 'VIP V3',\n        description: '高级VIP会员',\n        default_duration: 90,\n        color: '#8B5CF6'\n    },\n    v4: {\n        label: 'VIP V4',\n        description: '至尊VIP会员',\n        default_duration: 180,\n        color: '#F59E0B'\n    }\n};\n// 激活码管理服务类\nclass ActivationCodeService {\n    /**\n   * 获取激活码列表\n   */ static async getActivationCodes(params) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/activation-codes', params);\n    }\n    /**\n   * 根据ID获取激活码详情\n   */ static async getActivationCodeById(id) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/activation-codes/\".concat(id));\n    }\n    /**\n   * 创建激活码\n   */ static async createActivationCodes(data) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post('/activation-codes', data);\n    }\n    /**\n   * 更新激活码\n   */ static async updateActivationCode(id, data) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].patch(\"/activation-codes/\".concat(id), data);\n    }\n    /**\n   * 删除激活码\n   */ static async deleteActivationCode(id) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/activation-codes/\".concat(id));\n    }\n    /**\n   * 获取激活码统计信息\n   */ static async getActivationCodeStats() {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get('/activation-codes/stats');\n    }\n    /**\n   * 使用激活码 (用户端)\n   */ static async useActivationCode(data) {\n        return _lib_api_client__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post('/activation-codes/use', data);\n    }\n    /**\n   * 获取VIP等级配置\n   */ static getVipLevelConfig() {\n        return VIP_LEVEL_CONFIG;\n    }\n    /**\n   * 获取VIP等级标签\n   */ static getVipLevelLabel(level) {\n        var _VIP_LEVEL_CONFIG_level;\n        return ((_VIP_LEVEL_CONFIG_level = VIP_LEVEL_CONFIG[level]) === null || _VIP_LEVEL_CONFIG_level === void 0 ? void 0 : _VIP_LEVEL_CONFIG_level.label) || level;\n    }\n    /**\n   * 获取VIP等级颜色\n   */ static getVipLevelColor(level) {\n        var _VIP_LEVEL_CONFIG_level;\n        return ((_VIP_LEVEL_CONFIG_level = VIP_LEVEL_CONFIG[level]) === null || _VIP_LEVEL_CONFIG_level === void 0 ? void 0 : _VIP_LEVEL_CONFIG_level.color) || '#6B7280';\n    }\n    /**\n   * 获取状态标签\n   */ static getStatusLabel(status) {\n        const statusLabels = {\n            active: '有效',\n            used: '已使用',\n            expired: '已过期',\n            disabled: '已禁用'\n        };\n        return statusLabels[status] || status;\n    }\n    /**\n   * 获取状态颜色\n   */ static getStatusColor(status) {\n        const statusColors = {\n            active: '#10B981',\n            used: '#6B7280',\n            expired: '#EF4444',\n            disabled: '#F59E0B' // amber\n        };\n        return statusColors[status] || '#6B7280';\n    }\n    /**\n   * 格式化激活码显示\n   */ static formatCode(code) {\n        // 确保激活码以 XXXX-XXXX-XXXX-XXXX 格式显示\n        const cleanCode = code.replace(/[^A-Z0-9]/g, '');\n        return cleanCode.replace(/(.{4})/g, '$1-').slice(0, -1);\n    }\n    /**\n   * 验证激活码格式\n   */ static validateCodeFormat(code) {\n        const codePattern = /^[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}-[A-Z0-9]{4}$/;\n        return codePattern.test(code.trim().toUpperCase());\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ActivationCodeService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/services/activation-code.service.ts\n"));

/***/ })

});