/**
 * 认证辅助工具
 * 提供登录、注册、登出等认证相关功能
 */

import { authAPI } from '@/api/index.js';
import userManager from './user-manager.js';
import { ToastManager } from './toast-manager.js';

// 认证辅助类
export class AuthHelper {
  // 用户登录
  static async login(credentials) {
    try {
      const result = await authAPI.login(credentials);
      
      if (result.success && result.data) {
        // 保存用户信息和token
        await userManager.login(result.data.user, result.data.token);
        
        // 显示成功提示
        ToastManager.success('登录成功');
        
        return {
          success: true,
          user: result.data.user,
          token: result.data.token
        };
      } else {
        throw new Error(result.message || '登录失败');
      }
    } catch (error) {
      console.error('登录失败:', error);
      
      // 显示错误提示
      ToastManager.error(error.message || '登录失败，请重试');
      
      return {
        success: false,
        error: error.message || '登录失败'
      };
    }
  }

  // 用户注册
  static async register(userData) {
    try {
      const result = await authAPI.register(userData);
      
      if (result.success && result.data) {
        // 注册成功后自动登录
        await userManager.login(result.data.user, result.data.token);
        
        // 显示成功提示
        ToastManager.success('注册成功，欢迎加入！');
        
        return {
          success: true,
          user: result.data.user,
          token: result.data.token
        };
      } else {
        throw new Error(result.message || '注册失败');
      }
    } catch (error) {
      console.error('注册失败:', error);
      
      // 显示错误提示
      ToastManager.error(error.message || '注册失败，请重试');
      
      return {
        success: false,
        error: error.message || '注册失败'
      };
    }
  }

  // 用户登出
  static async logout() {
    try {
      // 调用API登出
      await authAPI.logout();
    } catch (error) {
      console.error('登出API调用失败:', error);
      // 即使API调用失败，也要清除本地数据
    }
    
    // 清除本地用户数据
    await userManager.logout();
    
    // 显示提示
    ToastManager.success('已退出登录');
    
    return {
      success: true
    };
  }

  // 检查登录状态
  static isLoggedIn() {
    return userManager.isUserLoggedIn();
  }

  // 获取当前用户信息
  static getCurrentUser() {
    return userManager.getUserInfo();
  }

  // 刷新用户信息
  static async refreshUserInfo() {
    try {
      const result = await authAPI.getUserInfo();
      
      if (result.success && result.data) {
        userManager.updateUserInfo(result.data);
        return {
          success: true,
          user: result.data
        };
      } else {
        throw new Error(result.message || '获取用户信息失败');
      }
    } catch (error) {
      console.error('刷新用户信息失败:', error);
      
      return {
        success: false,
        error: error.message || '获取用户信息失败'
      };
    }
  }

  // 检查是否需要登录
  static requireAuth() {
    if (!this.isLoggedIn()) {
      ToastManager.error('请先登录');
      this.redirectToLogin();
      return false;
    }
    return true;
  }

  // 跳转到登录页面
  static redirectToLogin() {
    uni.reLaunch({
      url: '/pages/login/login'
    });
  }

  // 跳转到首页
  static redirectToHome() {
    uni.reLaunch({
      url: '/pages/index/index'
    });
  }

  // 监听登录状态变化
  static onAuthStateChange(callback) {
    return userManager.addListener(callback);
  }

  // 验证VIP权限
  static checkVipAccess(requiredLevel = 'v1') {
    const user = this.getCurrentUser();
    if (!user) {
      ToastManager.error('请先登录');
      this.redirectToLogin();
      return false;
    }

    const userVipLevel = user.vip_level;
    if (!userVipLevel || userVipLevel === 'none') {
      ToastManager.error('此功能需要VIP权限');
      return false;
    }

    // 检查VIP是否过期
    if (userManager.isVipExpired()) {
      ToastManager.error('VIP权限已过期');
      return false;
    }

    // 简单的VIP等级比较（假设v1 < v2 < v3 < v4）
    const levelMap = { v1: 1, v2: 2, v3: 3, v4: 4 };
    const userLevel = levelMap[userVipLevel] || 0;
    const requiredLevelNum = levelMap[requiredLevel] || 1;

    if (userLevel < requiredLevelNum) {
      ToastManager.error(`此功能需要${requiredLevel}或更高等级的VIP权限`);
      return false;
    }

    return true;
  }

  // 处理认证错误
  static handleAuthError(error) {
    console.error('认证错误:', error);
    
    // 如果是token过期或无效，清除登录状态
    if (error.code === 'UNAUTHORIZED' || error.code === 'TOKEN_EXPIRED') {
      this.logout();
      ToastManager.error('登录已过期，请重新登录');
      this.redirectToLogin();
    } else {
      ToastManager.error(error.message || '认证失败');
    }
  }

  // 自动登录检查
  static async autoLoginCheck() {
    if (!this.isLoggedIn()) {
      return false;
    }

    // 验证登录状态
    const isValid = await userManager.validateLoginStatus();
    if (!isValid) {
      return false;
    }

    // 尝试刷新用户信息
    const result = await this.refreshUserInfo();
    return result.success;
  }
}

// 导出便捷方法
export const authUtils = {
  login: AuthHelper.login,
  register: AuthHelper.register,
  logout: AuthHelper.logout,
  isLoggedIn: AuthHelper.isLoggedIn,
  getCurrentUser: AuthHelper.getCurrentUser,
  requireAuth: AuthHelper.requireAuth,
  checkVipAccess: AuthHelper.checkVipAccess,
  onAuthStateChange: AuthHelper.onAuthStateChange,
  redirectToLogin: AuthHelper.redirectToLogin,
  redirectToHome: AuthHelper.redirectToHome
};

export default AuthHelper;
