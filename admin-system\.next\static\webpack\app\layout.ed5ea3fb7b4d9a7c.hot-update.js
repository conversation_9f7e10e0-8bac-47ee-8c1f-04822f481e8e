"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"2fd95d3ab91f\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJFOlxcN21vdXRoTWlzc2lvblxcdW5pRmlnbWFcXGFkbWluLXN5c3RlbVxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiMmZkOTVkM2FiOTFmXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/lib/api-client.ts":
/*!*******************************!*\
  !*** ./src/lib/api-client.ts ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ApiClient: () => (/* binding */ ApiClient),\n/* harmony export */   ApiClientError: () => (/* binding */ ApiClientError),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/build/polyfills/process.js\");\n/**\n * Admin System API 客户端\n * 提供类型安全的API调用接口\n */ // API 配置\nconst API_CONFIG = {\n    baseURL: process.env.NEXT_PUBLIC_API_URL || '/api',\n    timeout: 10000,\n    retryCount: 3,\n    retryDelay: 1000\n};\n// 错误类型\nclass ApiClientError extends Error {\n    constructor(statusCode, message, code, data){\n        super(message), this.statusCode = statusCode, this.code = code, this.data = data;\n        this.name = 'ApiClientError';\n    }\n}\n// Token 管理\nclass TokenManager {\n    static getToken() {\n        if (false) {}\n        return localStorage.getItem(this.TOKEN_KEY);\n    }\n    static setToken(token) {\n        if (false) {}\n        localStorage.setItem(this.TOKEN_KEY, token);\n    }\n    static clearToken() {\n        if (false) {}\n        localStorage.removeItem(this.TOKEN_KEY);\n        localStorage.removeItem(this.USER_KEY);\n    }\n    static getUser() {\n        if (false) {}\n        const userStr = localStorage.getItem(this.USER_KEY);\n        return userStr ? JSON.parse(userStr) : null;\n    }\n    static setUser(user) {\n        if (false) {}\n        localStorage.setItem(this.USER_KEY, JSON.stringify(user));\n    }\n}\nTokenManager.TOKEN_KEY = 'auth_token';\nTokenManager.USER_KEY = 'auth_user';\n// 请求拦截器\nfunction requestInterceptor(url, config) {\n    const token = TokenManager.getToken();\n    const headers = {\n        'Content-Type': 'application/json',\n        'X-Client-Type': 'admin-web',\n        'X-Client-Version': '1.0.0',\n        'X-Client-Platform': 'web',\n        ...config.headers\n    };\n    if (token) {\n        headers.Authorization = \"Bearer \".concat(token);\n    }\n    return {\n        ...config,\n        headers\n    };\n}\n// 响应拦截器\nasync function responseInterceptor(response) {\n    const contentType = response.headers.get('content-type');\n    let data;\n    if (contentType && contentType.includes('application/json')) {\n        data = await response.json();\n    } else {\n        data = await response.text();\n    }\n    if (!response.ok) {\n        // HTTP 错误\n        if (response.status === 401) {\n            // 未授权，清除token\n            TokenManager.clearToken();\n            // 如果在浏览器环境且不在登录页，跳转到登录页\n            if ( true && !window.location.pathname.includes('/login')) {\n                window.location.href = '/login';\n            }\n        }\n        throw new ApiClientError(response.status, (data === null || data === void 0 ? void 0 : data.error) || (data === null || data === void 0 ? void 0 : data.message) || \"HTTP \".concat(response.status), data === null || data === void 0 ? void 0 : data.code, data);\n    }\n    // 业务逻辑错误检查\n    if (data && typeof data === 'object' && data.success === false) {\n        throw new ApiClientError(response.status, data.error || data.message || '请求失败', data.code, data);\n    }\n    return data;\n}\n// 重试机制\nasync function withRetry(requestFn) {\n    let retryCount = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : API_CONFIG.retryCount;\n    let lastError;\n    for(let i = 0; i <= retryCount; i++){\n        try {\n            return await requestFn();\n        } catch (error) {\n            lastError = error;\n            // 如果是客户端错误（4xx）或最后一次重试，直接抛出错误\n            if (error instanceof ApiClientError && error.statusCode < 500) {\n                throw error;\n            }\n            if (i === retryCount) {\n                throw error;\n            }\n            // 等待后重试\n            await new Promise((resolve)=>setTimeout(resolve, API_CONFIG.retryDelay * (i + 1)));\n        }\n    }\n    throw lastError;\n}\n// 核心请求函数\nasync function request(endpoint) {\n    let config = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const url = \"\".concat(API_CONFIG.baseURL).concat(endpoint);\n    const interceptedConfig = requestInterceptor(url, config);\n    const requestFn = async ()=>{\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), config.timeout || API_CONFIG.timeout);\n        try {\n            const response = await fetch(url, {\n                method: config.method || 'GET',\n                headers: interceptedConfig.headers,\n                body: config.body ? JSON.stringify(config.body) : undefined,\n                signal: controller.signal\n            });\n            clearTimeout(timeoutId);\n            return await responseInterceptor(response);\n        } catch (error) {\n            clearTimeout(timeoutId);\n            if (error instanceof DOMException && error.name === 'AbortError') {\n                throw new ApiClientError(408, '请求超时', 'TIMEOUT');\n            }\n            throw error;\n        }\n    };\n    return withRetry(requestFn, config.retryCount);\n}\n// API 客户端类\nclass ApiClient {\n    // GET 请求\n    static async get(endpoint, params, config) {\n        let url = endpoint;\n        if (params) {\n            const searchParams = new URLSearchParams();\n            Object.entries(params).forEach((param)=>{\n                let [key, value] = param;\n                if (value !== undefined && value !== null) {\n                    searchParams.append(key, String(value));\n                }\n            });\n            const queryString = searchParams.toString();\n            if (queryString) {\n                url += \"?\".concat(queryString);\n            }\n        }\n        return request(url, {\n            ...config,\n            method: 'GET'\n        });\n    }\n    // POST 请求\n    static async post(endpoint, data, config) {\n        return request(endpoint, {\n            ...config,\n            method: 'POST',\n            body: data\n        });\n    }\n    // PUT 请求\n    static async put(endpoint, data, config) {\n        return request(endpoint, {\n            ...config,\n            method: 'PUT',\n            body: data\n        });\n    }\n    // DELETE 请求\n    static async delete(endpoint, config) {\n        return request(endpoint, {\n            ...config,\n            method: 'DELETE'\n        });\n    }\n    // PATCH 请求\n    static async patch(endpoint, data, config) {\n        return request(endpoint, {\n            ...config,\n            method: 'PATCH',\n            body: data\n        });\n    }\n    // 配置方法\n    static setBaseURL(url) {\n        API_CONFIG.baseURL = url;\n    }\n    static setTimeout(timeout) {\n        API_CONFIG.timeout = timeout;\n    }\n    static setRetryConfig(count, delay) {\n        API_CONFIG.retryCount = count;\n        API_CONFIG.retryDelay = delay;\n    }\n}\n// Token 管理方法\nApiClient.getToken = TokenManager.getToken;\nApiClient.setToken = TokenManager.setToken;\nApiClient.clearToken = TokenManager.clearToken;\nApiClient.getUser = TokenManager.getUser;\nApiClient.setUser = TokenManager.setUser;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ApiClient);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/api-client.ts\n"));

/***/ })

});