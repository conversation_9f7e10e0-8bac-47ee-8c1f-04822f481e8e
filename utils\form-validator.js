/**
 * 表单验证工具
 * 提供统一的表单验证规则和方法
 */

// 验证规则
export const validators = {
  // 必填验证
  required: (value, message = '此字段为必填项') => {
    if (!value || (typeof value === 'string' && !value.trim())) {
      return message;
    }
    return null;
  },

  // 最小长度验证
  minLength: (min, message) => (value) => {
    if (value && value.length < min) {
      return message || `最少需要${min}个字符`;
    }
    return null;
  },

  // 最大长度验证
  maxLength: (max, message) => (value) => {
    if (value && value.length > max) {
      return message || `最多允许${max}个字符`;
    }
    return null;
  },

  // 邮箱验证
  email: (value, message = '请输入有效的邮箱地址') => {
    if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
      return message;
    }
    return null;
  },

  // 账号验证（英文数字）
  username: (value, message = '账号只能包含英文字母和数字') => {
    if (value && !/^[a-zA-Z0-9]+$/.test(value)) {
      return message;
    }
    return null;
  },

  // 密码强度验证
  password: (value, message = '密码至少8位，包含字母和数字') => {
    if (value && (value.length < 8 || !/(?=.*[a-zA-Z])(?=.*\d)/.test(value))) {
      return message;
    }
    return null;
  },

  // 激活码格式验证
  activationCode: (value, message = '激活码格式不正确') => {
    if (value && !/^[A-Z0-9]{16,32}$/.test(value)) {
      return message;
    }
    return null;
  },

  // 确认密码验证
  confirmPassword: (originalPassword, message = '两次输入的密码不一致') => (value) => {
    if (value && value !== originalPassword) {
      return message;
    }
    return null;
  },

  // 手机号验证
  phone: (value, message = '请输入有效的手机号') => {
    if (value && !/^1[3-9]\d{9}$/.test(value)) {
      return message;
    }
    return null;
  },

  // 数字验证
  number: (value, message = '请输入有效的数字') => {
    if (value && isNaN(Number(value))) {
      return message;
    }
    return null;
  },

  // 正整数验证
  positiveInteger: (value, message = '请输入正整数') => {
    if (value && (!Number.isInteger(Number(value)) || Number(value) <= 0)) {
      return message;
    }
    return null;
  },

  // 自定义正则验证
  pattern: (regex, message = '格式不正确') => (value) => {
    if (value && !regex.test(value)) {
      return message;
    }
    return null;
  }
};

// 表单验证器类
export class FormValidator {
  constructor(rules = {}) {
    this.rules = rules;
    this.errors = {};
  }

  // 添加验证规则
  addRule(field, rule) {
    if (!this.rules[field]) {
      this.rules[field] = [];
    }
    this.rules[field].push(rule);
    return this;
  }

  // 验证单个字段
  validateField(field, value) {
    const fieldRules = this.rules[field];
    if (!fieldRules) return null;

    for (const rule of fieldRules) {
      const error = typeof rule === 'function' ? rule(value) : rule;
      if (error) {
        this.errors[field] = error;
        return error;
      }
    }

    // 验证通过，清除错误
    delete this.errors[field];
    return null;
  }

  // 验证所有字段
  validate(data) {
    this.errors = {};
    let isValid = true;

    for (const field in this.rules) {
      const error = this.validateField(field, data[field]);
      if (error) {
        isValid = false;
      }
    }

    return {
      isValid,
      errors: { ...this.errors }
    };
  }

  // 获取字段错误
  getFieldError(field) {
    return this.errors[field] || null;
  }

  // 获取所有错误
  getErrors() {
    return { ...this.errors };
  }

  // 清除字段错误
  clearFieldError(field) {
    delete this.errors[field];
  }

  // 清除所有错误
  clearErrors() {
    this.errors = {};
  }

  // 设置字段错误
  setFieldError(field, error) {
    this.errors[field] = error;
  }
}

// 预定义的表单验证规则
export const formRules = {
  // 登录表单
  login: {
    emailOrUsername: [
      validators.required('请输入账号或邮箱')
    ],
    password: [
      validators.required('请输入密码')
    ]
  },

  // 注册表单
  register: {
    username: [
      validators.required('请输入账号'),
      validators.minLength(3, '账号至少3位'),
      validators.maxLength(20, '账号不能超过20位'),
      validators.username('账号只能包含英文字母和数字')
    ],
    password: [
      validators.required('请输入密码'),
      validators.minLength(8, '密码至少8位'),
      validators.password('密码必须包含字母和数字')
    ],
    confirmPassword: [
      validators.required('请确认密码')
      // confirmPassword 验证需要在组件中动态添加
    ],
    activationCode: [
      validators.required('请输入激活码'),
      validators.activationCode('激活码格式不正确')
    ]
  },

  // 修改密码表单
  changePassword: {
    oldPassword: [
      validators.required('请输入当前密码')
    ],
    newPassword: [
      validators.required('请输入新密码'),
      validators.minLength(8, '密码至少8位'),
      validators.password('密码必须包含字母和数字')
    ],
    confirmNewPassword: [
      validators.required('请确认新密码')
    ]
  },

  // 个人资料表单
  profile: {
    full_name: [
      validators.maxLength(50, '姓名不能超过50个字符')
    ],
    email: [
      validators.email('请输入有效的邮箱地址')
    ],
    phone: [
      validators.phone('请输入有效的手机号')
    ]
  }
};

// 创建表单验证器的便捷方法
export function createValidator(type) {
  const rules = formRules[type];
  if (!rules) {
    throw new Error(`Unknown form type: ${type}`);
  }
  return new FormValidator(rules);
}

// 验证工具函数
export const validationUtils = {
  // 实时验证（防抖）
  debounceValidate: (validator, field, delay = 300) => {
    let timer = null;
    return (value, callback) => {
      if (timer) clearTimeout(timer);
      timer = setTimeout(() => {
        const error = validator.validateField(field, value);
        callback(error);
      }, delay);
    };
  },

  // 批量验证
  validateFields: (validator, data, fields) => {
    const errors = {};
    let isValid = true;

    for (const field of fields) {
      const error = validator.validateField(field, data[field]);
      if (error) {
        errors[field] = error;
        isValid = false;
      }
    }

    return { isValid, errors };
  },

  // 格式化错误信息
  formatErrors: (errors) => {
    return Object.values(errors).filter(Boolean).join('; ');
  }
};

export default {
  validators,
  FormValidator,
  formRules,
  createValidator,
  validationUtils
};
