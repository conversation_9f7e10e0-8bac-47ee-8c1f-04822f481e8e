<template>
  <view class="register-container">
    <!-- 状态栏 -->
    <view class="status-bar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="status-content">
        <view class="back-button" @tap="goBack">
          <text class="back-icon">←</text>
        </view>
        <text class="time-text">12:48</text>
        <view class="status-icons">
          <view class="signal-icon"></view>
          <view class="wifi-icon"></view>
          <view class="battery-icon">
            <view class="battery-level"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 主要内容 -->
    <view class="main-content">
      <!-- 标题区域 -->
      <view class="title-section">
        <text class="main-title">Sign up</text>
        <text class="subtitle">Create your account to get started with VIP access.</text>
      </view>

      <!-- 表单区域 -->
      <view class="form-section">
        <!-- 账号输入 -->
        <view class="input-group">
          <text class="input-label">Username</text>
          <view class="input-container">
            <view class="input-wrapper">
              <view class="input-icon">
                <text class="icon-user">👤</text>
              </view>
              <input
                class="form-input"
                type="text"
                v-model="formData.username"
                placeholder="Enter username"
                :disabled="loading"
                maxlength="20"
              />
            </view>
          </view>
          <view v-if="errors.username" class="error-text">
            {{ errors.username }}
          </view>
        </view>

        <!-- 密码输入 -->
        <view class="input-group">
          <text class="input-label">Password</text>
          <view class="input-container">
            <view class="input-wrapper">
              <view class="input-icon">
                <text class="icon-lock">🔒</text>
              </view>
              <input
                class="form-input password-input"
                :type="showPassword ? 'text' : 'password'"
                v-model="formData.password"
                placeholder="********"
                :disabled="loading"
              />
              <view
                class="password-toggle"
                @tap="togglePassword"
              >
                <text class="toggle-icon">{{ showPassword ? '👁️' : '👁️‍🗨️' }}</text>
              </view>
            </view>
          </view>
          <view v-if="errors.password" class="error-text">
            {{ errors.password }}
          </view>
        </view>

        <!-- 确认密码输入 -->
        <view class="input-group">
          <text class="input-label">Confirm Password</text>
          <view class="input-container">
            <view class="input-wrapper">
              <view class="input-icon">
                <text class="icon-lock">🔒</text>
              </view>
              <input
                class="form-input password-input"
                :type="showConfirmPassword ? 'text' : 'password'"
                v-model="formData.confirmPassword"
                placeholder="********"
                :disabled="loading"
              />
              <view
                class="password-toggle"
                @tap="toggleConfirmPassword"
              >
                <text class="toggle-icon">{{ showConfirmPassword ? '👁️' : '👁️‍🗨️' }}</text>
              </view>
            </view>
          </view>
          <view v-if="errors.confirmPassword" class="error-text">
            {{ errors.confirmPassword }}
          </view>
        </view>

        <!-- 激活码输入 -->
        <view class="input-group">
          <text class="input-label">Activation Code</text>
          <view class="input-container">
            <view class="input-wrapper">
              <view class="input-icon">
                <text class="icon-key">🔑</text>
              </view>
              <input
                class="form-input activation-code-input"
                type="text"
                v-model="formData.activationCode"
                placeholder="Enter activation code"
                :disabled="loading"
                style="text-transform: uppercase;"
              />
            </view>
          </view>
          <view v-if="errors.activationCode" class="error-text">
            {{ errors.activationCode }}
          </view>
          <view class="activation-code-tip">
            <text>Activation code is required for VIP access</text>
          </view>
        </view>

        <!-- 错误提示 -->
        <view v-if="errors.general" class="error-message">
          {{ errors.general }}
        </view>

        <!-- 注册按钮 -->
        <button
          class="register-btn"
          :class="{ 'loading': loading, 'disabled': !canSubmit }"
          :disabled="!canSubmit || loading"
          @tap="handleRegister"
        >
          <text v-if="loading">Creating...</text>
          <text v-else>Qeydiyyat</text>
        </button>

        <!-- 登录链接 -->
        <view class="login-section">
          <text class="login-text">Already have an account?</text>
          <text class="login-link" @tap="goToLogin">Sign in</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { createValidator, validators } from '@/utils/form-validator.js';
import { AuthHelper } from '@/utils/auth-helper.js';

export default {
  data() {
    return {
      statusBarHeight: 0,
      formData: {
        username: '',
        password: '',
        confirmPassword: '',
        activationCode: ''
      },
      showPassword: false,
      showConfirmPassword: false,
      loading: false,
      errors: {},
      validator: null
    };
  },

  created() {
    // 创建表单验证器
    this.validator = createValidator('register');
    // 添加确认密码验证规则
    this.validator.addRule('confirmPassword', validators.confirmPassword(this.formData.password));
  },

  computed: {
    canSubmit() {
      return this.formData.username.trim() && 
             this.formData.password.trim() && 
             this.formData.confirmPassword.trim() &&
             this.formData.activationCode.trim() &&
             !this.loading;
    }
  },

  onLoad() {
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight || 0;
  },

  methods: {
    // 返回上一页
    goBack() {
      uni.navigateBack();
    },

    // 切换密码显示
    togglePassword() {
      this.showPassword = !this.showPassword;
    },

    // 切换确认密码显示
    toggleConfirmPassword() {
      this.showConfirmPassword = !this.showConfirmPassword;
    },

    // 表单验证
    validateForm() {
      // 更新确认密码验证规则
      this.validator.rules.confirmPassword = [
        validators.required('请确认密码'),
        validators.confirmPassword(this.formData.password)
      ];

      const { isValid, errors } = this.validator.validate(this.formData);
      this.errors = errors;
      return isValid;
    },

    // 验证单个字段
    validateField(field) {
      // 特殊处理确认密码验证
      if (field === 'confirmPassword') {
        this.validator.rules.confirmPassword = [
          validators.required('请确认密码'),
          validators.confirmPassword(this.formData.password)
        ];
      }

      const error = this.validator.validateField(field, this.formData[field]);
      if (error) {
        this.$set(this.errors, field, error);
      } else {
        this.$delete(this.errors, field);
      }
      return !error;
    },

    // 处理注册
    async handleRegister() {
      if (!this.validateForm()) {
        return;
      }

      this.loading = true;
      this.errors = {};

      try {
        const result = await AuthHelper.register({
          username: this.formData.username.trim(),
          email: this.formData.username.trim() + '@temp.com', // 临时邮箱，后续可以让用户补充
          password: this.formData.password,
          activation_code: this.formData.activationCode.trim().toUpperCase()
        });

        if (result.success) {
          // 注册成功，延迟跳转
          setTimeout(() => {
            uni.reLaunch({
              url: '/pages/index/index'
            });
          }, 2000);
        } else {
          this.errors.general = result.error || '注册失败，请重试';
        }

      } catch (error) {
        console.error('注册失败:', error);
        this.errors.general = error.message || '注册失败，请重试';
      } finally {
        this.loading = false;
      }
    },

    // 跳转到登录页面
    goToLogin() {
      uni.navigateBack();
    },

    // 清除错误
    clearError(field) {
      if (this.errors[field]) {
        this.$delete(this.errors, field);
      }
    }
  },

  watch: {
    'formData.username'() {
      this.clearError('username');
      this.clearError('general');
    },
    'formData.password'() {
      this.clearError('password');
      this.clearError('general');
    },
    'formData.confirmPassword'() {
      this.clearError('confirmPassword');
      this.clearError('general');
    },
    'formData.activationCode'() {
      this.clearError('activationCode');
      this.clearError('general');
      // 自动转换为大写
      this.formData.activationCode = this.formData.activationCode.toUpperCase();
    }
  }
};
</script>

<style scoped>
.register-container {
  width: 100%;
  min-height: 100vh;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: 40px;
}

/* 状态栏 */
.status-bar {
  width: 100%;
  height: 88px;
  position: relative;
}

.status-content {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32px;
  padding-top: 28px;
  position: relative;
}

.back-button {
  position: absolute;
  left: 32px;
  top: 28px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.back-icon {
  font-size: 24px;
  color: #000000;
  font-weight: bold;
}

.time-text {
  font-family: 'Montserrat', sans-serif;
  font-size: 30px;
  font-weight: 600;
  color: #000000;
  letter-spacing: -0.3px;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.status-icons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.signal-icon, .wifi-icon {
  width: 30px;
  height: 20px;
  background: #000000;
  border-radius: 2px;
}

.battery-icon {
  width: 44px;
  height: 21px;
  border: 2px solid #000000;
  border-radius: 4px;
  position: relative;
  display: flex;
  align-items: center;
  padding: 2px;
}

.battery-level {
  width: 36px;
  height: 13px;
  background: #000000;
  border-radius: 2px;
}

/* 主要内容 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 60px;
}

/* 标题区域 */
.title-section {
  width: 100%;
  text-align: center;
  margin-top: 60px;
  margin-bottom: 60px;
}

.main-title {
  font-family: 'Samsung Sharp Sans', sans-serif;
  font-size: 64px;
  font-weight: bold;
  color: #F2282D;
  line-height: 80px;
  letter-spacing: -0.32px;
  display: block;
  margin-bottom: 8px;
}

.subtitle {
  font-family: 'Samsung Sharp Sans', sans-serif;
  font-size: 30px;
  font-weight: 500;
  color: #8A8A8A;
  line-height: 44px;
  display: block;
}

/* 表单区域 */
.form-section {
  width: 100%;
  max-width: 670px;
}

.input-group {
  margin-bottom: 20px;
}

.input-label {
  font-family: 'Samsung Sharp Sans', sans-serif;
  font-size: 24px;
  font-weight: 500;
  color: #000000;
  letter-spacing: 1.2px;
  line-height: 32.86px;
  display: block;
  margin-bottom: 18px;
}

.input-container {
  position: relative;
  width: 100%;
  height: 108px;
}

.input-wrapper {
  width: 100%;
  height: 100%;
  border: 2.34px solid #D1D1D1;
  border-radius: 200px;
  display: flex;
  align-items: center;
  padding: 0 46px;
  background: #ffffff;
  position: relative;
}

.input-icon {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.icon-user, .icon-lock, .icon-key {
  font-size: 24px;
}

.form-input {
  flex: 1;
  border: none;
  outline: none;
  font-family: 'Samsung Sharp Sans', sans-serif;
  font-size: 24px;
  font-weight: 500;
  color: #000000;
  letter-spacing: 1.2px;
  background: transparent;
}

.form-input::placeholder {
  color: #989898;
  font-weight: 500;
}

.password-input {
  font-size: 36px;
  letter-spacing: 0.9px;
  font-weight: normal;
}

.activation-code-input {
  font-family: 'Courier New', monospace;
  letter-spacing: 2px;
  text-transform: uppercase;
}

.password-toggle {
  position: absolute;
  right: 46px;
  top: 50%;
  transform: translateY(-50%);
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  cursor: pointer;
}

.toggle-icon {
  font-size: 20px;
}

.activation-code-tip {
  margin-top: 8px;
  font-size: 14px;
  color: #8A8A8A;
  text-align: center;
  line-height: 1.4;
}

/* 错误提示 */
.error-text {
  color: #F2282D;
  font-size: 14px;
  margin-top: 8px;
  text-align: center;
}

.error-message {
  background: #ffe6e6;
  color: #F2282D;
  padding: 12px;
  border-radius: 12px;
  font-size: 14px;
  margin-bottom: 20px;
  text-align: center;
}

/* 注册按钮 */
.register-btn {
  width: 100%;
  height: 108px;
  background: #F2282D;
  border: none;
  border-radius: 200px;
  margin-top: 60px;
  margin-bottom: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.register-btn text {
  font-family: 'Samsung Sharp Sans', sans-serif;
  font-size: 32px;
  font-weight: bold;
  color: #ffffff;
}

.register-btn.disabled {
  opacity: 0.6;
  background: #ccc;
}

.register-btn.loading {
  opacity: 0.8;
}

.register-btn:active {
  transform: scale(0.98);
}

/* 登录链接 */
.login-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  margin-top: 14px;
}

.login-text {
  font-family: 'Samsung Sharp Sans', sans-serif;
  font-size: 28px;
  font-weight: 500;
  color: #000000;
  letter-spacing: 0.14px;
  line-height: 51.62px;
}

.login-link {
  font-family: 'Samsung Sharp Sans', sans-serif;
  font-size: 30px;
  font-weight: bold;
  color: #F2282D;
  cursor: pointer;
}
</style>
