<template>
  <view class="login-container">
    <!-- 状态栏 -->
    <view class="status-bar" :style="{ paddingTop: statusBarHeight + 'px' }">
      <view class="status-content">
        <text class="time-text">12:48</text>
        <view class="status-icons">
          <view class="signal-icon"></view>
          <view class="wifi-icon"></view>
          <view class="battery-icon">
            <view class="battery-level"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 主要内容 -->
    <view class="main-content">
      <!-- 标题区域 -->
      <view class="title-section">
        <text class="main-title">Sign in</text>
        <text class="subtitle">When entering data, please pay attention to its accuracy.</text>
      </view>

      <!-- 表单区域 -->
      <view class="form-section">
        <!-- 邮箱输入 -->
        <view class="input-group">
          <text class="input-label">Email</text>
          <view class="input-container">
            <view class="input-wrapper">
              <view class="input-icon">
                <text class="icon-email">📧</text>
              </view>
              <input
                class="form-input"
                type="text"
                v-model="formData.emailOrUsername"
                placeholder="<EMAIL>"
                :disabled="loading"
              />
            </view>
          </view>
          <view v-if="errors.emailOrUsername" class="error-text">
            {{ errors.emailOrUsername }}
          </view>
        </view>

        <!-- 密码输入 -->
        <view class="input-group">
          <text class="input-label">Password</text>
          <view class="input-container">
            <view class="input-wrapper">
              <view class="input-icon">
                <text class="icon-lock">🔒</text>
              </view>
              <input
                class="form-input password-input"
                :type="showPassword ? 'text' : 'password'"
                v-model="formData.password"
                placeholder="********"
                :disabled="loading"
              />
              <view
                class="password-toggle"
                @tap="togglePassword"
              >
                <text class="toggle-icon">{{ showPassword ? '👁️' : '👁️‍🗨️' }}</text>
              </view>
            </view>
          </view>
          <view v-if="errors.password" class="error-text">
            {{ errors.password }}
          </view>
        </view>

        <!-- 忘记密码 -->
        <view class="forgot-password">
          <text class="forgot-link" @tap="handleForgotPassword">Forgot your password?</text>
        </view>

        <!-- 错误提示 -->
        <view v-if="errors.general" class="error-message">
          {{ errors.general }}
        </view>

        <!-- 登录按钮 -->
        <button
          class="login-btn"
          :class="{ 'loading': loading, 'disabled': !canSubmit }"
          :disabled="!canSubmit || loading"
          @tap="handleLogin"
        >
          <text v-if="loading">登录中...</text>
          <text v-else>Daxil ol</text>
        </button>

        <!-- 注册链接 -->
        <view class="register-section">
          <text class="register-text">Don't have an account?</text>
          <text class="register-link" @tap="goToRegister">Sign up</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import { createValidator } from '@/utils/form-validator.js';
import { AuthHelper } from '@/utils/auth-helper.js';

export default {
  data() {
    return {
      statusBarHeight: 0,
      formData: {
        emailOrUsername: '',
        password: ''
      },
      showPassword: false,
      loading: false,
      errors: {},
      validator: null
    };
  },

  created() {
    // 创建表单验证器
    this.validator = createValidator('login');
  },

  computed: {
    canSubmit() {
      return this.formData.emailOrUsername.trim() && 
             this.formData.password.trim() && 
             !this.loading;
    }
  },

  onLoad() {
    // 获取状态栏高度
    const systemInfo = uni.getSystemInfoSync();
    this.statusBarHeight = systemInfo.statusBarHeight || 0;
  },

  methods: {
    // 切换密码显示
    togglePassword() {
      this.showPassword = !this.showPassword;
    },

    // 表单验证
    validateForm() {
      const { isValid, errors } = this.validator.validate(this.formData);
      this.errors = errors;
      return isValid;
    },

    // 验证单个字段
    validateField(field) {
      const error = this.validator.validateField(field, this.formData[field]);
      if (error) {
        this.$set(this.errors, field, error);
      } else {
        this.$delete(this.errors, field);
      }
      return !error;
    },

    // 处理登录
    async handleLogin() {
      if (!this.validateForm()) {
        return;
      }

      this.loading = true;
      this.errors = {};

      try {
        const result = await AuthHelper.login({
          emailOrUsername: this.formData.emailOrUsername.trim(),
          password: this.formData.password
        });

        if (result.success) {
          // 登录成功，跳转到首页
          setTimeout(() => {
            uni.reLaunch({
              url: '/pages/index/index'
            });
          }, 1000);
        } else {
          this.errors.general = result.error || '登录失败，请重试';
        }

      } catch (error) {
        console.error('登录失败:', error);
        this.errors.general = error.message || '登录失败，请重试';
      } finally {
        this.loading = false;
      }
    },

    // 跳转到注册页面
    goToRegister() {
      uni.navigateTo({
        url: '/pages/register/register'
      });
    },

    // 处理忘记密码
    handleForgotPassword() {
      uni.showToast({
        title: '忘记密码功能开发中',
        icon: 'none',
        duration: 2000
      });
    },

    // 清除错误
    clearError(field) {
      if (this.errors[field]) {
        this.$delete(this.errors, field);
      }
    }
  },

  watch: {
    'formData.emailOrUsername'() {
      this.clearError('emailOrUsername');
      this.clearError('general');
    },
    'formData.password'() {
      this.clearError('password');
      this.clearError('general');
    }
  }
};
</script>

<style scoped>
.login-container {
  width: 100%;
  min-height: 100vh;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border-radius: 40px;
}

/* 状态栏 */
.status-bar {
  width: 100%;
  height: 88px;
  position: relative;
}

.status-content {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 32px;
  padding-top: 28px;
}

.time-text {
  font-family: 'Montserrat', sans-serif;
  font-size: 30px;
  font-weight: 600;
  color: #000000;
  letter-spacing: -0.3px;
}

.status-icons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.signal-icon, .wifi-icon {
  width: 30px;
  height: 20px;
  background: #000000;
  border-radius: 2px;
}

.battery-icon {
  width: 44px;
  height: 21px;
  border: 2px solid #000000;
  border-radius: 4px;
  position: relative;
  display: flex;
  align-items: center;
  padding: 2px;
}

.battery-level {
  width: 36px;
  height: 13px;
  background: #000000;
  border-radius: 2px;
}

/* 主要内容 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 60px;
}

/* 标题区域 */
.title-section {
  width: 100%;
  text-align: center;
  margin-top: 96px;
  margin-bottom: 106px;
}

.main-title {
  font-family: 'Samsung Sharp Sans', sans-serif;
  font-size: 64px;
  font-weight: bold;
  color: #F2282D;
  line-height: 80px;
  letter-spacing: -0.32px;
  display: block;
  margin-bottom: 8px;
}

.subtitle {
  font-family: 'Samsung Sharp Sans', sans-serif;
  font-size: 30px;
  font-weight: 500;
  color: #8A8A8A;
  line-height: 44px;
  display: block;
}

/* 表单区域 */
.form-section {
  width: 100%;
  max-width: 670px;
}

.input-group {
  margin-bottom: 24px;
}

.input-label {
  font-family: 'Samsung Sharp Sans', sans-serif;
  font-size: 24px;
  font-weight: 500;
  color: #000000;
  letter-spacing: 1.2px;
  line-height: 32.86px;
  display: block;
  margin-bottom: 18px;
}

.input-container {
  position: relative;
  width: 100%;
  height: 108px;
}

.input-wrapper {
  width: 100%;
  height: 100%;
  border: 2.34px solid #D1D1D1;
  border-radius: 200px;
  display: flex;
  align-items: center;
  padding: 0 46px;
  background: #ffffff;
  position: relative;
}

.input-icon {
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
}

.icon-email, .icon-lock {
  font-size: 24px;
}

.form-input {
  flex: 1;
  border: none;
  outline: none;
  font-family: 'Samsung Sharp Sans', sans-serif;
  font-size: 24px;
  font-weight: 500;
  color: #000000;
  letter-spacing: 1.2px;
  background: transparent;
}

.form-input::placeholder {
  color: #989898;
  font-weight: 500;
}

.password-input {
  font-size: 36px;
  letter-spacing: 0.9px;
  font-weight: normal;
}

.password-toggle {
  position: absolute;
  right: 46px;
  top: 50%;
  transform: translateY(-50%);
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10px;
  cursor: pointer;
}

.toggle-icon {
  font-size: 20px;
}

/* 忘记密码 */
.forgot-password {
  text-align: center;
  margin: 20px 0;
}

.forgot-link {
  font-family: 'Samsung Sharp Sans', sans-serif;
  font-size: 26px;
  font-weight: bold;
  color: #F2282D;
  letter-spacing: 0.13px;
  line-height: 51.62px;
  cursor: pointer;
}

/* 错误提示 */
.error-text {
  color: #F2282D;
  font-size: 14px;
  margin-top: 8px;
  text-align: center;
}

.error-message {
  background: #ffe6e6;
  color: #F2282D;
  padding: 12px;
  border-radius: 12px;
  font-size: 14px;
  margin-bottom: 20px;
  text-align: center;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 108px;
  background: #F2282D;
  border: none;
  border-radius: 200px;
  margin-top: 80px;
  margin-bottom: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.login-btn text {
  font-family: 'Samsung Sharp Sans', sans-serif;
  font-size: 32px;
  font-weight: bold;
  color: #ffffff;
}

.login-btn.disabled {
  opacity: 0.6;
  background: #ccc;
}

.login-btn.loading {
  opacity: 0.8;
}

.login-btn:active {
  transform: scale(0.98);
}

/* 注册链接 */
.register-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
  margin-top: 14px;
}

.register-text {
  font-family: 'Samsung Sharp Sans', sans-serif;
  font-size: 28px;
  font-weight: 500;
  color: #000000;
  letter-spacing: 0.14px;
  line-height: 51.62px;
}

.register-link {
  font-family: 'Samsung Sharp Sans', sans-serif;
  font-size: 30px;
  font-weight: bold;
  color: #F2282D;
  cursor: pointer;
}
</style>
