"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/sonner@2.0.6_react-dom@19.1.0_react@19.1.0__react@19.1.0";
exports.ids = ["vendor-chunks/sonner@2.0.6_react-dom@19.1.0_react@19.1.0__react@19.1.0"];
exports.modules = {

/***/ "(rsc)/./node_modules/.pnpm/sonner@2.0.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/sonner@2.0.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   Toaster: () => (/* binding */ Toaster),
/* harmony export */   toast: () => (/* binding */ toast),
/* harmony export */   useSonner: () => (/* binding */ useSonner)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server */ "(rsc)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server.js");

const Toaster = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\7mouthMission\\uniFigma\\admin-system\\node_modules\\.pnpm\\sonner@2.0.6_react-dom@19.1.0_react@19.1.0__react@19.1.0\\node_modules\\sonner\\dist\\index.mjs",
"Toaster",
);const toast = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call toast() from the server but toast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\7mouthMission\\uniFigma\\admin-system\\node_modules\\.pnpm\\sonner@2.0.6_react-dom@19.1.0_react@19.1.0__react@19.1.0\\node_modules\\sonner\\dist\\index.mjs",
"toast",
);const useSonner = (0,react_server_dom_webpack_server__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call useSonner() from the server but useSonner is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"E:\\7mouthMission\\uniFigma\\admin-system\\node_modules\\.pnpm\\sonner@2.0.6_react-dom@19.1.0_react@19.1.0__react@19.1.0\\node_modules\\sonner\\dist\\index.mjs",
"useSonner",
);

/***/ }),

/***/ "(ssr)/./node_modules/.pnpm/sonner@2.0.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs":
/*!************************************************************************************************************************!*\
  !*** ./node_modules/.pnpm/sonner@2.0.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs ***!
  \************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Toaster: () => (/* binding */ Toaster),\n/* harmony export */   toast: () => (/* binding */ toast),\n/* harmony export */   useSonner: () => (/* binding */ useSonner)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* __next_internal_client_entry_do_not_use__ Toaster,toast,useSonner auto */ function __insertCSS(code) {\n    if (!code || typeof document == 'undefined') return;\n    let head = document.head || document.getElementsByTagName('head')[0];\n    let style = document.createElement('style');\n    style.type = 'text/css';\n    head.appendChild(style);\n    style.styleSheet ? style.styleSheet.cssText = code : style.appendChild(document.createTextNode(code));\n}\n\n\nconst getAsset = (type)=>{\n    switch(type){\n        case 'success':\n            return SuccessIcon;\n        case 'info':\n            return InfoIcon;\n        case 'warning':\n            return WarningIcon;\n        case 'error':\n            return ErrorIcon;\n        default:\n            return null;\n    }\n};\nconst bars = Array(12).fill(0);\nconst Loader = ({ visible, className })=>{\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: [\n            'sonner-loading-wrapper',\n            className\n        ].filter(Boolean).join(' '),\n        \"data-visible\": visible\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        className: \"sonner-spinner\"\n    }, bars.map((_, i)=>/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n            className: \"sonner-loading-bar\",\n            key: `spinner-bar-${i}`\n        }))));\n};\nconst SuccessIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z\",\n    clipRule: \"evenodd\"\n}));\nconst WarningIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 24 24\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M9.401 3.003c1.155-2 4.043-2 5.197 0l7.355 12.748c1.154 2-.29 4.5-2.599 4.5H4.645c-2.309 0-3.752-2.5-2.598-4.5L9.4 3.003zM12 8.25a.75.75 0 01.75.75v3.75a.75.75 0 01-1.5 0V9a.75.75 0 01.75-.75zm0 8.25a.75.75 0 100-********* 0 000 1.5z\",\n    clipRule: \"evenodd\"\n}));\nconst InfoIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a.75.75 0 000 1.5h.253a.25.25 0 01.244.304l-.459 2.066A1.75 1.75 0 0010.747 15H11a.75.75 0 000-1.5h-.253a.25.25 0 01-.244-.304l.459-2.066A1.75 1.75 0 009.253 9H9z\",\n    clipRule: \"evenodd\"\n}));\nconst ErrorIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    viewBox: \"0 0 20 20\",\n    fill: \"currentColor\",\n    height: \"20\",\n    width: \"20\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n    fillRule: \"evenodd\",\n    d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z\",\n    clipRule: \"evenodd\"\n}));\nconst CloseIcon = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", {\n    xmlns: \"http://www.w3.org/2000/svg\",\n    width: \"12\",\n    height: \"12\",\n    viewBox: \"0 0 24 24\",\n    fill: \"none\",\n    stroke: \"currentColor\",\n    strokeWidth: \"1.5\",\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\"\n}, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"18\",\n    y1: \"6\",\n    x2: \"6\",\n    y2: \"18\"\n}), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"line\", {\n    x1: \"6\",\n    y1: \"6\",\n    x2: \"18\",\n    y2: \"18\"\n}));\nconst useIsDocumentHidden = ()=>{\n    const [isDocumentHidden, setIsDocumentHidden] = react__WEBPACK_IMPORTED_MODULE_0__.useState(document.hidden);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useIsDocumentHidden.useEffect\": ()=>{\n            const callback = {\n                \"useIsDocumentHidden.useEffect.callback\": ()=>{\n                    setIsDocumentHidden(document.hidden);\n                }\n            }[\"useIsDocumentHidden.useEffect.callback\"];\n            document.addEventListener('visibilitychange', callback);\n            return ({\n                \"useIsDocumentHidden.useEffect\": ()=>window.removeEventListener('visibilitychange', callback)\n            })[\"useIsDocumentHidden.useEffect\"];\n        }\n    }[\"useIsDocumentHidden.useEffect\"], []);\n    return isDocumentHidden;\n};\nlet toastsCounter = 1;\nclass Observer {\n    constructor(){\n        // We use arrow functions to maintain the correct `this` reference\n        this.subscribe = (subscriber)=>{\n            this.subscribers.push(subscriber);\n            return ()=>{\n                const index = this.subscribers.indexOf(subscriber);\n                this.subscribers.splice(index, 1);\n            };\n        };\n        this.publish = (data)=>{\n            this.subscribers.forEach((subscriber)=>subscriber(data));\n        };\n        this.addToast = (data)=>{\n            this.publish(data);\n            this.toasts = [\n                ...this.toasts,\n                data\n            ];\n        };\n        this.create = (data)=>{\n            var _data_id;\n            const { message, ...rest } = data;\n            const id = typeof (data == null ? void 0 : data.id) === 'number' || ((_data_id = data.id) == null ? void 0 : _data_id.length) > 0 ? data.id : toastsCounter++;\n            const alreadyExists = this.toasts.find((toast)=>{\n                return toast.id === id;\n            });\n            const dismissible = data.dismissible === undefined ? true : data.dismissible;\n            if (this.dismissedToasts.has(id)) {\n                this.dismissedToasts.delete(id);\n            }\n            if (alreadyExists) {\n                this.toasts = this.toasts.map((toast)=>{\n                    if (toast.id === id) {\n                        this.publish({\n                            ...toast,\n                            ...data,\n                            id,\n                            title: message\n                        });\n                        return {\n                            ...toast,\n                            ...data,\n                            id,\n                            dismissible,\n                            title: message\n                        };\n                    }\n                    return toast;\n                });\n            } else {\n                this.addToast({\n                    title: message,\n                    ...rest,\n                    dismissible,\n                    id\n                });\n            }\n            return id;\n        };\n        this.dismiss = (id)=>{\n            if (id) {\n                this.dismissedToasts.add(id);\n                requestAnimationFrame(()=>this.subscribers.forEach((subscriber)=>subscriber({\n                            id,\n                            dismiss: true\n                        })));\n            } else {\n                this.toasts.forEach((toast)=>{\n                    this.subscribers.forEach((subscriber)=>subscriber({\n                            id: toast.id,\n                            dismiss: true\n                        }));\n                });\n            }\n            return id;\n        };\n        this.message = (message, data)=>{\n            return this.create({\n                ...data,\n                message\n            });\n        };\n        this.error = (message, data)=>{\n            return this.create({\n                ...data,\n                message,\n                type: 'error'\n            });\n        };\n        this.success = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'success',\n                message\n            });\n        };\n        this.info = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'info',\n                message\n            });\n        };\n        this.warning = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'warning',\n                message\n            });\n        };\n        this.loading = (message, data)=>{\n            return this.create({\n                ...data,\n                type: 'loading',\n                message\n            });\n        };\n        this.promise = (promise, data)=>{\n            if (!data) {\n                // Nothing to show\n                return;\n            }\n            let id = undefined;\n            if (data.loading !== undefined) {\n                id = this.create({\n                    ...data,\n                    promise,\n                    type: 'loading',\n                    message: data.loading,\n                    description: typeof data.description !== 'function' ? data.description : undefined\n                });\n            }\n            const p = Promise.resolve(promise instanceof Function ? promise() : promise);\n            let shouldDismiss = id !== undefined;\n            let result;\n            const originalPromise = p.then(async (response)=>{\n                result = [\n                    'resolve',\n                    response\n                ];\n                const isReactElementResponse = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(response);\n                if (isReactElementResponse) {\n                    shouldDismiss = false;\n                    this.create({\n                        id,\n                        type: 'default',\n                        message: response\n                    });\n                } else if (isHttpResponse(response) && !response.ok) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(`HTTP error! status: ${response.status}`) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(`HTTP error! status: ${response.status}`) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                } else if (response instanceof Error) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(response) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(response) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                } else if (data.success !== undefined) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.success === 'function' ? await data.success(response) : data.success;\n                    const description = typeof data.description === 'function' ? await data.description(response) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'success',\n                        description,\n                        ...toastSettings\n                    });\n                }\n            }).catch(async (error)=>{\n                result = [\n                    'reject',\n                    error\n                ];\n                if (data.error !== undefined) {\n                    shouldDismiss = false;\n                    const promiseData = typeof data.error === 'function' ? await data.error(error) : data.error;\n                    const description = typeof data.description === 'function' ? await data.description(error) : data.description;\n                    const isExtendedResult = typeof promiseData === 'object' && !/*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(promiseData);\n                    const toastSettings = isExtendedResult ? promiseData : {\n                        message: promiseData\n                    };\n                    this.create({\n                        id,\n                        type: 'error',\n                        description,\n                        ...toastSettings\n                    });\n                }\n            }).finally(()=>{\n                if (shouldDismiss) {\n                    // Toast is still in load state (and will be indefinitely — dismiss it)\n                    this.dismiss(id);\n                    id = undefined;\n                }\n                data.finally == null ? void 0 : data.finally.call(data);\n            });\n            const unwrap = ()=>new Promise((resolve, reject)=>originalPromise.then(()=>result[0] === 'reject' ? reject(result[1]) : resolve(result[1])).catch(reject));\n            if (typeof id !== 'string' && typeof id !== 'number') {\n                // cannot Object.assign on undefined\n                return {\n                    unwrap\n                };\n            } else {\n                return Object.assign(id, {\n                    unwrap\n                });\n            }\n        };\n        this.custom = (jsx, data)=>{\n            const id = (data == null ? void 0 : data.id) || toastsCounter++;\n            this.create({\n                jsx: jsx(id),\n                id,\n                ...data\n            });\n            return id;\n        };\n        this.getActiveToasts = ()=>{\n            return this.toasts.filter((toast)=>!this.dismissedToasts.has(toast.id));\n        };\n        this.subscribers = [];\n        this.toasts = [];\n        this.dismissedToasts = new Set();\n    }\n}\nconst ToastState = new Observer();\n// bind this to the toast function\nconst toastFunction = (message, data)=>{\n    const id = (data == null ? void 0 : data.id) || toastsCounter++;\n    ToastState.addToast({\n        title: message,\n        ...data,\n        id\n    });\n    return id;\n};\nconst isHttpResponse = (data)=>{\n    return data && typeof data === 'object' && 'ok' in data && typeof data.ok === 'boolean' && 'status' in data && typeof data.status === 'number';\n};\nconst basicToast = toastFunction;\nconst getHistory = ()=>ToastState.toasts;\nconst getToasts = ()=>ToastState.getActiveToasts();\n// We use `Object.assign` to maintain the correct types as we would lose them otherwise\nconst toast = Object.assign(basicToast, {\n    success: ToastState.success,\n    info: ToastState.info,\n    warning: ToastState.warning,\n    error: ToastState.error,\n    custom: ToastState.custom,\n    message: ToastState.message,\n    promise: ToastState.promise,\n    dismiss: ToastState.dismiss,\n    loading: ToastState.loading\n}, {\n    getHistory,\n    getToasts\n});\n__insertCSS(\"[data-sonner-toaster][dir=ltr],html[dir=ltr]{--toast-icon-margin-start:-3px;--toast-icon-margin-end:4px;--toast-svg-margin-start:-1px;--toast-svg-margin-end:0px;--toast-button-margin-start:auto;--toast-button-margin-end:0;--toast-close-button-start:0;--toast-close-button-end:unset;--toast-close-button-transform:translate(-35%, -35%)}[data-sonner-toaster][dir=rtl],html[dir=rtl]{--toast-icon-margin-start:4px;--toast-icon-margin-end:-3px;--toast-svg-margin-start:0px;--toast-svg-margin-end:-1px;--toast-button-margin-start:0;--toast-button-margin-end:auto;--toast-close-button-start:unset;--toast-close-button-end:0;--toast-close-button-transform:translate(35%, -35%)}[data-sonner-toaster]{position:fixed;width:var(--width);font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;--gray1:hsl(0, 0%, 99%);--gray2:hsl(0, 0%, 97.3%);--gray3:hsl(0, 0%, 95.1%);--gray4:hsl(0, 0%, 93%);--gray5:hsl(0, 0%, 90.9%);--gray6:hsl(0, 0%, 88.7%);--gray7:hsl(0, 0%, 85.8%);--gray8:hsl(0, 0%, 78%);--gray9:hsl(0, 0%, 56.1%);--gray10:hsl(0, 0%, 52.3%);--gray11:hsl(0, 0%, 43.5%);--gray12:hsl(0, 0%, 9%);--border-radius:8px;box-sizing:border-box;padding:0;margin:0;list-style:none;outline:0;z-index:999999999;transition:transform .4s ease}@media (hover:none) and (pointer:coarse){[data-sonner-toaster][data-lifted=true]{transform:none}}[data-sonner-toaster][data-x-position=right]{right:var(--offset-right)}[data-sonner-toaster][data-x-position=left]{left:var(--offset-left)}[data-sonner-toaster][data-x-position=center]{left:50%;transform:translateX(-50%)}[data-sonner-toaster][data-y-position=top]{top:var(--offset-top)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--offset-bottom)}[data-sonner-toast]{--y:translateY(100%);--lift-amount:calc(var(--lift) * var(--gap));z-index:var(--z-index);position:absolute;opacity:0;transform:var(--y);touch-action:none;transition:transform .4s,opacity .4s,height .4s,box-shadow .2s;box-sizing:border-box;outline:0;overflow-wrap:anywhere}[data-sonner-toast][data-styled=true]{padding:16px;background:var(--normal-bg);border:1px solid var(--normal-border);color:var(--normal-text);border-radius:var(--border-radius);box-shadow:0 4px 12px rgba(0,0,0,.1);width:var(--width);font-size:13px;display:flex;align-items:center;gap:6px}[data-sonner-toast]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-y-position=top]{top:0;--y:translateY(-100%);--lift:1;--lift-amount:calc(1 * var(--gap))}[data-sonner-toast][data-y-position=bottom]{bottom:0;--y:translateY(100%);--lift:-1;--lift-amount:calc(var(--lift) * var(--gap))}[data-sonner-toast][data-styled=true] [data-description]{font-weight:400;line-height:1.4;color:#3f3f3f}[data-rich-colors=true][data-sonner-toast][data-styled=true] [data-description]{color:inherit}[data-sonner-toaster][data-sonner-theme=dark] [data-description]{color:#e8e8e8}[data-sonner-toast][data-styled=true] [data-title]{font-weight:500;line-height:1.5;color:inherit}[data-sonner-toast][data-styled=true] [data-icon]{display:flex;height:16px;width:16px;position:relative;justify-content:flex-start;align-items:center;flex-shrink:0;margin-left:var(--toast-icon-margin-start);margin-right:var(--toast-icon-margin-end)}[data-sonner-toast][data-promise=true] [data-icon]>svg{opacity:0;transform:scale(.8);transform-origin:center;animation:sonner-fade-in .3s ease forwards}[data-sonner-toast][data-styled=true] [data-icon]>*{flex-shrink:0}[data-sonner-toast][data-styled=true] [data-icon] svg{margin-left:var(--toast-svg-margin-start);margin-right:var(--toast-svg-margin-end)}[data-sonner-toast][data-styled=true] [data-content]{display:flex;flex-direction:column;gap:2px}[data-sonner-toast][data-styled=true] [data-button]{border-radius:4px;padding-left:8px;padding-right:8px;height:24px;font-size:12px;color:var(--normal-bg);background:var(--normal-text);margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end);border:none;font-weight:500;cursor:pointer;outline:0;display:flex;align-items:center;flex-shrink:0;transition:opacity .4s,box-shadow .2s}[data-sonner-toast][data-styled=true] [data-button]:focus-visible{box-shadow:0 0 0 2px rgba(0,0,0,.4)}[data-sonner-toast][data-styled=true] [data-button]:first-of-type{margin-left:var(--toast-button-margin-start);margin-right:var(--toast-button-margin-end)}[data-sonner-toast][data-styled=true] [data-cancel]{color:var(--normal-text);background:rgba(0,0,0,.08)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-styled=true] [data-cancel]{background:rgba(255,255,255,.3)}[data-sonner-toast][data-styled=true] [data-close-button]{position:absolute;left:var(--toast-close-button-start);right:var(--toast-close-button-end);top:0;height:20px;width:20px;display:flex;justify-content:center;align-items:center;padding:0;color:var(--gray12);background:var(--normal-bg);border:1px solid var(--gray4);transform:var(--toast-close-button-transform);border-radius:50%;cursor:pointer;z-index:1;transition:opacity .1s,background .2s,border-color .2s}[data-sonner-toast][data-styled=true] [data-close-button]:focus-visible{box-shadow:0 4px 12px rgba(0,0,0,.1),0 0 0 2px rgba(0,0,0,.2)}[data-sonner-toast][data-styled=true] [data-disabled=true]{cursor:not-allowed}[data-sonner-toast][data-styled=true]:hover [data-close-button]:hover{background:var(--gray2);border-color:var(--gray5)}[data-sonner-toast][data-swiping=true]::before{content:'';position:absolute;left:-100%;right:-100%;height:100%;z-index:-1}[data-sonner-toast][data-y-position=top][data-swiping=true]::before{bottom:50%;transform:scaleY(3) translateY(50%)}[data-sonner-toast][data-y-position=bottom][data-swiping=true]::before{top:50%;transform:scaleY(3) translateY(-50%)}[data-sonner-toast][data-swiping=false][data-removed=true]::before{content:'';position:absolute;inset:0;transform:scaleY(2)}[data-sonner-toast][data-expanded=true]::after{content:'';position:absolute;left:0;height:calc(var(--gap) + 1px);bottom:100%;width:100%}[data-sonner-toast][data-mounted=true]{--y:translateY(0);opacity:1}[data-sonner-toast][data-expanded=false][data-front=false]{--scale:var(--toasts-before) * 0.05 + 1;--y:translateY(calc(var(--lift-amount) * var(--toasts-before))) scale(calc(-1 * var(--scale)));height:var(--front-toast-height)}[data-sonner-toast]>*{transition:opacity .4s}[data-sonner-toast][data-x-position=right]{right:0}[data-sonner-toast][data-x-position=left]{left:0}[data-sonner-toast][data-expanded=false][data-front=false][data-styled=true]>*{opacity:0}[data-sonner-toast][data-visible=false]{opacity:0;pointer-events:none}[data-sonner-toast][data-mounted=true][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset)));height:var(--initial-height)}[data-sonner-toast][data-removed=true][data-front=true][data-swipe-out=false]{--y:translateY(calc(var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=true]{--y:translateY(calc(var(--lift) * var(--offset) + var(--lift) * -100%));opacity:0}[data-sonner-toast][data-removed=true][data-front=false][data-swipe-out=false][data-expanded=false]{--y:translateY(40%);opacity:0;transition:transform .5s,opacity .2s}[data-sonner-toast][data-removed=true][data-front=false]::before{height:calc(var(--initial-height) + 20%)}[data-sonner-toast][data-swiping=true]{transform:var(--y) translateY(var(--swipe-amount-y,0)) translateX(var(--swipe-amount-x,0));transition:none}[data-sonner-toast][data-swiped=true]{user-select:none}[data-sonner-toast][data-swipe-out=true][data-y-position=bottom],[data-sonner-toast][data-swipe-out=true][data-y-position=top]{animation-duration:.2s;animation-timing-function:ease-out;animation-fill-mode:forwards}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=left]{animation-name:swipe-out-left}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=right]{animation-name:swipe-out-right}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=up]{animation-name:swipe-out-up}[data-sonner-toast][data-swipe-out=true][data-swipe-direction=down]{animation-name:swipe-out-down}@keyframes swipe-out-left{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) - 100%));opacity:0}}@keyframes swipe-out-right{from{transform:var(--y) translateX(var(--swipe-amount-x));opacity:1}to{transform:var(--y) translateX(calc(var(--swipe-amount-x) + 100%));opacity:0}}@keyframes swipe-out-up{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) - 100%));opacity:0}}@keyframes swipe-out-down{from{transform:var(--y) translateY(var(--swipe-amount-y));opacity:1}to{transform:var(--y) translateY(calc(var(--swipe-amount-y) + 100%));opacity:0}}@media (max-width:600px){[data-sonner-toaster]{position:fixed;right:var(--mobile-offset-right);left:var(--mobile-offset-left);width:100%}[data-sonner-toaster][dir=rtl]{left:calc(var(--mobile-offset-left) * -1)}[data-sonner-toaster] [data-sonner-toast]{left:0;right:0;width:calc(100% - var(--mobile-offset-left) * 2)}[data-sonner-toaster][data-x-position=left]{left:var(--mobile-offset-left)}[data-sonner-toaster][data-y-position=bottom]{bottom:var(--mobile-offset-bottom)}[data-sonner-toaster][data-y-position=top]{top:var(--mobile-offset-top)}[data-sonner-toaster][data-x-position=center]{left:var(--mobile-offset-left);right:var(--mobile-offset-right);transform:none}}[data-sonner-toaster][data-sonner-theme=light]{--normal-bg:#fff;--normal-border:var(--gray4);--normal-text:var(--gray12);--success-bg:hsl(143, 85%, 96%);--success-border:hsl(145, 92%, 87%);--success-text:hsl(140, 100%, 27%);--info-bg:hsl(208, 100%, 97%);--info-border:hsl(221, 91%, 93%);--info-text:hsl(210, 92%, 45%);--warning-bg:hsl(49, 100%, 97%);--warning-border:hsl(49, 91%, 84%);--warning-text:hsl(31, 92%, 45%);--error-bg:hsl(359, 100%, 97%);--error-border:hsl(359, 100%, 94%);--error-text:hsl(360, 100%, 45%)}[data-sonner-toaster][data-sonner-theme=light] [data-sonner-toast][data-invert=true]{--normal-bg:#000;--normal-border:hsl(0, 0%, 20%);--normal-text:var(--gray1)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast][data-invert=true]{--normal-bg:#fff;--normal-border:var(--gray3);--normal-text:var(--gray12)}[data-sonner-toaster][data-sonner-theme=dark]{--normal-bg:#000;--normal-bg-hover:hsl(0, 0%, 12%);--normal-border:hsl(0, 0%, 20%);--normal-border-hover:hsl(0, 0%, 25%);--normal-text:var(--gray1);--success-bg:hsl(150, 100%, 6%);--success-border:hsl(147, 100%, 12%);--success-text:hsl(150, 86%, 65%);--info-bg:hsl(215, 100%, 6%);--info-border:hsl(223, 43%, 17%);--info-text:hsl(216, 87%, 65%);--warning-bg:hsl(64, 100%, 6%);--warning-border:hsl(60, 100%, 9%);--warning-text:hsl(46, 87%, 65%);--error-bg:hsl(358, 76%, 10%);--error-border:hsl(357, 89%, 16%);--error-text:hsl(358, 100%, 81%)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]{background:var(--normal-bg);border-color:var(--normal-border);color:var(--normal-text)}[data-sonner-toaster][data-sonner-theme=dark] [data-sonner-toast] [data-close-button]:hover{background:var(--normal-bg-hover);border-color:var(--normal-border-hover)}[data-rich-colors=true][data-sonner-toast][data-type=success]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=success] [data-close-button]{background:var(--success-bg);border-color:var(--success-border);color:var(--success-text)}[data-rich-colors=true][data-sonner-toast][data-type=info]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=info] [data-close-button]{background:var(--info-bg);border-color:var(--info-border);color:var(--info-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=warning] [data-close-button]{background:var(--warning-bg);border-color:var(--warning-border);color:var(--warning-text)}[data-rich-colors=true][data-sonner-toast][data-type=error]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}[data-rich-colors=true][data-sonner-toast][data-type=error] [data-close-button]{background:var(--error-bg);border-color:var(--error-border);color:var(--error-text)}.sonner-loading-wrapper{--size:16px;height:var(--size);width:var(--size);position:absolute;inset:0;z-index:10}.sonner-loading-wrapper[data-visible=false]{transform-origin:center;animation:sonner-fade-out .2s ease forwards}.sonner-spinner{position:relative;top:50%;left:50%;height:var(--size);width:var(--size)}.sonner-loading-bar{animation:sonner-spin 1.2s linear infinite;background:var(--gray11);border-radius:6px;height:8%;left:-10%;position:absolute;top:-3.9%;width:24%}.sonner-loading-bar:first-child{animation-delay:-1.2s;transform:rotate(.0001deg) translate(146%)}.sonner-loading-bar:nth-child(2){animation-delay:-1.1s;transform:rotate(30deg) translate(146%)}.sonner-loading-bar:nth-child(3){animation-delay:-1s;transform:rotate(60deg) translate(146%)}.sonner-loading-bar:nth-child(4){animation-delay:-.9s;transform:rotate(90deg) translate(146%)}.sonner-loading-bar:nth-child(5){animation-delay:-.8s;transform:rotate(120deg) translate(146%)}.sonner-loading-bar:nth-child(6){animation-delay:-.7s;transform:rotate(150deg) translate(146%)}.sonner-loading-bar:nth-child(7){animation-delay:-.6s;transform:rotate(180deg) translate(146%)}.sonner-loading-bar:nth-child(8){animation-delay:-.5s;transform:rotate(210deg) translate(146%)}.sonner-loading-bar:nth-child(9){animation-delay:-.4s;transform:rotate(240deg) translate(146%)}.sonner-loading-bar:nth-child(10){animation-delay:-.3s;transform:rotate(270deg) translate(146%)}.sonner-loading-bar:nth-child(11){animation-delay:-.2s;transform:rotate(300deg) translate(146%)}.sonner-loading-bar:nth-child(12){animation-delay:-.1s;transform:rotate(330deg) translate(146%)}@keyframes sonner-fade-in{0%{opacity:0;transform:scale(.8)}100%{opacity:1;transform:scale(1)}}@keyframes sonner-fade-out{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(.8)}}@keyframes sonner-spin{0%{opacity:1}100%{opacity:.15}}@media (prefers-reduced-motion){.sonner-loading-bar,[data-sonner-toast],[data-sonner-toast]>*{transition:none!important;animation:none!important}}.sonner-loader{position:absolute;top:50%;left:50%;transform:translate(-50%,-50%);transform-origin:center;transition:opacity .2s,transform .2s}.sonner-loader[data-visible=false]{opacity:0;transform:scale(.8) translate(-50%,-50%)}\");\nfunction isAction(action) {\n    return action.label !== undefined;\n}\n// Visible toasts amount\nconst VISIBLE_TOASTS_AMOUNT = 3;\n// Viewport padding\nconst VIEWPORT_OFFSET = '24px';\n// Mobile viewport padding\nconst MOBILE_VIEWPORT_OFFSET = '16px';\n// Default lifetime of a toasts (in ms)\nconst TOAST_LIFETIME = 4000;\n// Default toast width\nconst TOAST_WIDTH = 356;\n// Default gap between toasts\nconst GAP = 14;\n// Threshold to dismiss a toast\nconst SWIPE_THRESHOLD = 45;\n// Equal to exit animation duration\nconst TIME_BEFORE_UNMOUNT = 200;\nfunction cn(...classes) {\n    return classes.filter(Boolean).join(' ');\n}\nfunction getDefaultSwipeDirections(position) {\n    const [y, x] = position.split('-');\n    const directions = [];\n    if (y) {\n        directions.push(y);\n    }\n    if (x) {\n        directions.push(x);\n    }\n    return directions;\n}\nconst Toast = (props)=>{\n    var _toast_classNames, _toast_classNames1, _toast_classNames2, _toast_classNames3, _toast_classNames4, _toast_classNames5, _toast_classNames6, _toast_classNames7, _toast_classNames8;\n    const { invert: ToasterInvert, toast, unstyled, interacting, setHeights, visibleToasts, heights, index, toasts, expanded, removeToast, defaultRichColors, closeButton: closeButtonFromToaster, style, cancelButtonStyle, actionButtonStyle, className = '', descriptionClassName = '', duration: durationFromToaster, position, gap, expandByDefault, classNames, icons, closeButtonAriaLabel = 'Close toast' } = props;\n    const [swipeDirection, setSwipeDirection] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [swipeOutDirection, setSwipeOutDirection] = react__WEBPACK_IMPORTED_MODULE_0__.useState(null);\n    const [mounted, setMounted] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [removed, setRemoved] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [swiping, setSwiping] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [swipeOut, setSwipeOut] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [isSwiped, setIsSwiped] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [offsetBeforeRemove, setOffsetBeforeRemove] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const [initialHeight, setInitialHeight] = react__WEBPACK_IMPORTED_MODULE_0__.useState(0);\n    const remainingTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(toast.duration || durationFromToaster || TOAST_LIFETIME);\n    const dragStartTime = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const toastRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFront = index === 0;\n    const isVisible = index + 1 <= visibleToasts;\n    const toastType = toast.type;\n    const dismissible = toast.dismissible !== false;\n    const toastClassname = toast.className || '';\n    const toastDescriptionClassname = toast.descriptionClassName || '';\n    // Height index is used to calculate the offset as it gets updated before the toast array, which means we can calculate the new layout faster.\n    const heightIndex = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[heightIndex]\": ()=>heights.findIndex({\n                \"Toast.useMemo[heightIndex]\": (height)=>height.toastId === toast.id\n            }[\"Toast.useMemo[heightIndex]\"]) || 0\n    }[\"Toast.useMemo[heightIndex]\"], [\n        heights,\n        toast.id\n    ]);\n    const closeButton = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[closeButton]\": ()=>{\n            var _toast_closeButton;\n            return (_toast_closeButton = toast.closeButton) != null ? _toast_closeButton : closeButtonFromToaster;\n        }\n    }[\"Toast.useMemo[closeButton]\"], [\n        toast.closeButton,\n        closeButtonFromToaster\n    ]);\n    const duration = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[duration]\": ()=>toast.duration || durationFromToaster || TOAST_LIFETIME\n    }[\"Toast.useMemo[duration]\"], [\n        toast.duration,\n        durationFromToaster\n    ]);\n    const closeTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const offset = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const lastCloseTimerStartTimeRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(0);\n    const pointerStartRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const [y, x] = position.split('-');\n    const toastsHeightBefore = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo[toastsHeightBefore]\": ()=>{\n            return heights.reduce({\n                \"Toast.useMemo[toastsHeightBefore]\": (prev, curr, reducerIndex)=>{\n                    // Calculate offset up until current toast\n                    if (reducerIndex >= heightIndex) {\n                        return prev;\n                    }\n                    return prev + curr.height;\n                }\n            }[\"Toast.useMemo[toastsHeightBefore]\"], 0);\n        }\n    }[\"Toast.useMemo[toastsHeightBefore]\"], [\n        heights,\n        heightIndex\n    ]);\n    const isDocumentHidden = useIsDocumentHidden();\n    const invert = toast.invert || ToasterInvert;\n    const disabled = toastType === 'loading';\n    offset.current = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toast.useMemo\": ()=>heightIndex * gap + toastsHeightBefore\n    }[\"Toast.useMemo\"], [\n        heightIndex,\n        toastsHeightBefore\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            remainingTime.current = duration;\n        }\n    }[\"Toast.useEffect\"], [\n        duration\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            // Trigger enter animation without using CSS animation\n            setMounted(true);\n        }\n    }[\"Toast.useEffect\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            const toastNode = toastRef.current;\n            if (toastNode) {\n                const height = toastNode.getBoundingClientRect().height;\n                // Add toast height to heights array after the toast is mounted\n                setInitialHeight(height);\n                setHeights({\n                    \"Toast.useEffect\": (h)=>[\n                            {\n                                toastId: toast.id,\n                                height,\n                                position: toast.position\n                            },\n                            ...h\n                        ]\n                }[\"Toast.useEffect\"]);\n                return ({\n                    \"Toast.useEffect\": ()=>setHeights({\n                            \"Toast.useEffect\": (h)=>h.filter({\n                                    \"Toast.useEffect\": (height)=>height.toastId !== toast.id\n                                }[\"Toast.useEffect\"])\n                        }[\"Toast.useEffect\"])\n                })[\"Toast.useEffect\"];\n            }\n        }\n    }[\"Toast.useEffect\"], [\n        setHeights,\n        toast.id\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect({\n        \"Toast.useLayoutEffect\": ()=>{\n            // Keep height up to date with the content in case it updates\n            if (!mounted) return;\n            const toastNode = toastRef.current;\n            const originalHeight = toastNode.style.height;\n            toastNode.style.height = 'auto';\n            const newHeight = toastNode.getBoundingClientRect().height;\n            toastNode.style.height = originalHeight;\n            setInitialHeight(newHeight);\n            setHeights({\n                \"Toast.useLayoutEffect\": (heights)=>{\n                    const alreadyExists = heights.find({\n                        \"Toast.useLayoutEffect.alreadyExists\": (height)=>height.toastId === toast.id\n                    }[\"Toast.useLayoutEffect.alreadyExists\"]);\n                    if (!alreadyExists) {\n                        return [\n                            {\n                                toastId: toast.id,\n                                height: newHeight,\n                                position: toast.position\n                            },\n                            ...heights\n                        ];\n                    } else {\n                        return heights.map({\n                            \"Toast.useLayoutEffect\": (height)=>height.toastId === toast.id ? {\n                                    ...height,\n                                    height: newHeight\n                                } : height\n                        }[\"Toast.useLayoutEffect\"]);\n                    }\n                }\n            }[\"Toast.useLayoutEffect\"]);\n        }\n    }[\"Toast.useLayoutEffect\"], [\n        mounted,\n        toast.title,\n        toast.description,\n        setHeights,\n        toast.id,\n        toast.jsx,\n        toast.action,\n        toast.cancel\n    ]);\n    const deleteToast = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Toast.useCallback[deleteToast]\": ()=>{\n            // Save the offset for the exit swipe animation\n            setRemoved(true);\n            setOffsetBeforeRemove(offset.current);\n            setHeights({\n                \"Toast.useCallback[deleteToast]\": (h)=>h.filter({\n                        \"Toast.useCallback[deleteToast]\": (height)=>height.toastId !== toast.id\n                    }[\"Toast.useCallback[deleteToast]\"])\n            }[\"Toast.useCallback[deleteToast]\"]);\n            setTimeout({\n                \"Toast.useCallback[deleteToast]\": ()=>{\n                    removeToast(toast);\n                }\n            }[\"Toast.useCallback[deleteToast]\"], TIME_BEFORE_UNMOUNT);\n        }\n    }[\"Toast.useCallback[deleteToast]\"], [\n        toast,\n        removeToast,\n        setHeights,\n        offset\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            if (toast.promise && toastType === 'loading' || toast.duration === Infinity || toast.type === 'loading') return;\n            let timeoutId;\n            // Pause the timer on each hover\n            const pauseTimer = {\n                \"Toast.useEffect.pauseTimer\": ()=>{\n                    if (lastCloseTimerStartTimeRef.current < closeTimerStartTimeRef.current) {\n                        // Get the elapsed time since the timer started\n                        const elapsedTime = new Date().getTime() - closeTimerStartTimeRef.current;\n                        remainingTime.current = remainingTime.current - elapsedTime;\n                    }\n                    lastCloseTimerStartTimeRef.current = new Date().getTime();\n                }\n            }[\"Toast.useEffect.pauseTimer\"];\n            const startTimer = {\n                \"Toast.useEffect.startTimer\": ()=>{\n                    // setTimeout(, Infinity) behaves as if the delay is 0.\n                    // As a result, the toast would be closed immediately, giving the appearance that it was never rendered.\n                    // See: https://github.com/denysdovhan/wtfjs?tab=readme-ov-file#an-infinite-timeout\n                    if (remainingTime.current === Infinity) return;\n                    closeTimerStartTimeRef.current = new Date().getTime();\n                    // Let the toast know it has started\n                    timeoutId = setTimeout({\n                        \"Toast.useEffect.startTimer\": ()=>{\n                            toast.onAutoClose == null ? void 0 : toast.onAutoClose.call(toast, toast);\n                            deleteToast();\n                        }\n                    }[\"Toast.useEffect.startTimer\"], remainingTime.current);\n                }\n            }[\"Toast.useEffect.startTimer\"];\n            if (expanded || interacting || isDocumentHidden) {\n                pauseTimer();\n            } else {\n                startTimer();\n            }\n            return ({\n                \"Toast.useEffect\": ()=>clearTimeout(timeoutId)\n            })[\"Toast.useEffect\"];\n        }\n    }[\"Toast.useEffect\"], [\n        expanded,\n        interacting,\n        toast,\n        toastType,\n        isDocumentHidden,\n        deleteToast\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toast.useEffect\": ()=>{\n            if (toast.delete) {\n                deleteToast();\n                toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n            }\n        }\n    }[\"Toast.useEffect\"], [\n        deleteToast,\n        toast.delete\n    ]);\n    function getLoadingIcon() {\n        var _toast_classNames;\n        if (icons == null ? void 0 : icons.loading) {\n            var _toast_classNames1;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n                className: cn(classNames == null ? void 0 : classNames.loader, toast == null ? void 0 : (_toast_classNames1 = toast.classNames) == null ? void 0 : _toast_classNames1.loader, 'sonner-loader'),\n                \"data-visible\": toastType === 'loading'\n            }, icons.loading);\n        }\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Loader, {\n            className: cn(classNames == null ? void 0 : classNames.loader, toast == null ? void 0 : (_toast_classNames = toast.classNames) == null ? void 0 : _toast_classNames.loader),\n            visible: toastType === 'loading'\n        });\n    }\n    const icon = toast.icon || (icons == null ? void 0 : icons[toastType]) || getAsset(toastType);\n    var _toast_richColors, _icons_close;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"li\", {\n        tabIndex: 0,\n        ref: toastRef,\n        className: cn(className, toastClassname, classNames == null ? void 0 : classNames.toast, toast == null ? void 0 : (_toast_classNames = toast.classNames) == null ? void 0 : _toast_classNames.toast, classNames == null ? void 0 : classNames.default, classNames == null ? void 0 : classNames[toastType], toast == null ? void 0 : (_toast_classNames1 = toast.classNames) == null ? void 0 : _toast_classNames1[toastType]),\n        \"data-sonner-toast\": \"\",\n        \"data-rich-colors\": (_toast_richColors = toast.richColors) != null ? _toast_richColors : defaultRichColors,\n        \"data-styled\": !Boolean(toast.jsx || toast.unstyled || unstyled),\n        \"data-mounted\": mounted,\n        \"data-promise\": Boolean(toast.promise),\n        \"data-swiped\": isSwiped,\n        \"data-removed\": removed,\n        \"data-visible\": isVisible,\n        \"data-y-position\": y,\n        \"data-x-position\": x,\n        \"data-index\": index,\n        \"data-front\": isFront,\n        \"data-swiping\": swiping,\n        \"data-dismissible\": dismissible,\n        \"data-type\": toastType,\n        \"data-invert\": invert,\n        \"data-swipe-out\": swipeOut,\n        \"data-swipe-direction\": swipeOutDirection,\n        \"data-expanded\": Boolean(expanded || expandByDefault && mounted),\n        style: {\n            '--index': index,\n            '--toasts-before': index,\n            '--z-index': toasts.length - index,\n            '--offset': `${removed ? offsetBeforeRemove : offset.current}px`,\n            '--initial-height': expandByDefault ? 'auto' : `${initialHeight}px`,\n            ...style,\n            ...toast.style\n        },\n        onDragEnd: ()=>{\n            setSwiping(false);\n            setSwipeDirection(null);\n            pointerStartRef.current = null;\n        },\n        onPointerDown: (event)=>{\n            if (event.button === 2) return; // Return early on right click\n            if (disabled || !dismissible) return;\n            dragStartTime.current = new Date();\n            setOffsetBeforeRemove(offset.current);\n            // Ensure we maintain correct pointer capture even when going outside of the toast (e.g. when swiping)\n            event.target.setPointerCapture(event.pointerId);\n            if (event.target.tagName === 'BUTTON') return;\n            setSwiping(true);\n            pointerStartRef.current = {\n                x: event.clientX,\n                y: event.clientY\n            };\n        },\n        onPointerUp: ()=>{\n            var _toastRef_current, _toastRef_current1, _dragStartTime_current;\n            if (swipeOut || !dismissible) return;\n            pointerStartRef.current = null;\n            const swipeAmountX = Number(((_toastRef_current = toastRef.current) == null ? void 0 : _toastRef_current.style.getPropertyValue('--swipe-amount-x').replace('px', '')) || 0);\n            const swipeAmountY = Number(((_toastRef_current1 = toastRef.current) == null ? void 0 : _toastRef_current1.style.getPropertyValue('--swipe-amount-y').replace('px', '')) || 0);\n            const timeTaken = new Date().getTime() - ((_dragStartTime_current = dragStartTime.current) == null ? void 0 : _dragStartTime_current.getTime());\n            const swipeAmount = swipeDirection === 'x' ? swipeAmountX : swipeAmountY;\n            const velocity = Math.abs(swipeAmount) / timeTaken;\n            if (Math.abs(swipeAmount) >= SWIPE_THRESHOLD || velocity > 0.11) {\n                setOffsetBeforeRemove(offset.current);\n                toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n                if (swipeDirection === 'x') {\n                    setSwipeOutDirection(swipeAmountX > 0 ? 'right' : 'left');\n                } else {\n                    setSwipeOutDirection(swipeAmountY > 0 ? 'down' : 'up');\n                }\n                deleteToast();\n                setSwipeOut(true);\n                return;\n            } else {\n                var _toastRef_current2, _toastRef_current3;\n                (_toastRef_current2 = toastRef.current) == null ? void 0 : _toastRef_current2.style.setProperty('--swipe-amount-x', `0px`);\n                (_toastRef_current3 = toastRef.current) == null ? void 0 : _toastRef_current3.style.setProperty('--swipe-amount-y', `0px`);\n            }\n            setIsSwiped(false);\n            setSwiping(false);\n            setSwipeDirection(null);\n        },\n        onPointerMove: (event)=>{\n            var _window_getSelection, _toastRef_current, _toastRef_current1;\n            if (!pointerStartRef.current || !dismissible) return;\n            const isHighlighted = ((_window_getSelection = window.getSelection()) == null ? void 0 : _window_getSelection.toString().length) > 0;\n            if (isHighlighted) return;\n            const yDelta = event.clientY - pointerStartRef.current.y;\n            const xDelta = event.clientX - pointerStartRef.current.x;\n            var _props_swipeDirections;\n            const swipeDirections = (_props_swipeDirections = props.swipeDirections) != null ? _props_swipeDirections : getDefaultSwipeDirections(position);\n            // Determine swipe direction if not already locked\n            if (!swipeDirection && (Math.abs(xDelta) > 1 || Math.abs(yDelta) > 1)) {\n                setSwipeDirection(Math.abs(xDelta) > Math.abs(yDelta) ? 'x' : 'y');\n            }\n            let swipeAmount = {\n                x: 0,\n                y: 0\n            };\n            const getDampening = (delta)=>{\n                const factor = Math.abs(delta) / 20;\n                return 1 / (1.5 + factor);\n            };\n            // Only apply swipe in the locked direction\n            if (swipeDirection === 'y') {\n                // Handle vertical swipes\n                if (swipeDirections.includes('top') || swipeDirections.includes('bottom')) {\n                    if (swipeDirections.includes('top') && yDelta < 0 || swipeDirections.includes('bottom') && yDelta > 0) {\n                        swipeAmount.y = yDelta;\n                    } else {\n                        // Smoothly transition to dampened movement\n                        const dampenedDelta = yDelta * getDampening(yDelta);\n                        // Ensure we don't jump when transitioning to dampened movement\n                        swipeAmount.y = Math.abs(dampenedDelta) < Math.abs(yDelta) ? dampenedDelta : yDelta;\n                    }\n                }\n            } else if (swipeDirection === 'x') {\n                // Handle horizontal swipes\n                if (swipeDirections.includes('left') || swipeDirections.includes('right')) {\n                    if (swipeDirections.includes('left') && xDelta < 0 || swipeDirections.includes('right') && xDelta > 0) {\n                        swipeAmount.x = xDelta;\n                    } else {\n                        // Smoothly transition to dampened movement\n                        const dampenedDelta = xDelta * getDampening(xDelta);\n                        // Ensure we don't jump when transitioning to dampened movement\n                        swipeAmount.x = Math.abs(dampenedDelta) < Math.abs(xDelta) ? dampenedDelta : xDelta;\n                    }\n                }\n            }\n            if (Math.abs(swipeAmount.x) > 0 || Math.abs(swipeAmount.y) > 0) {\n                setIsSwiped(true);\n            }\n            (_toastRef_current = toastRef.current) == null ? void 0 : _toastRef_current.style.setProperty('--swipe-amount-x', `${swipeAmount.x}px`);\n            (_toastRef_current1 = toastRef.current) == null ? void 0 : _toastRef_current1.style.setProperty('--swipe-amount-y', `${swipeAmount.y}px`);\n        }\n    }, closeButton && !toast.jsx && toastType !== 'loading' ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"aria-label\": closeButtonAriaLabel,\n        \"data-disabled\": disabled,\n        \"data-close-button\": true,\n        onClick: disabled || !dismissible ? ()=>{} : ()=>{\n            deleteToast();\n            toast.onDismiss == null ? void 0 : toast.onDismiss.call(toast, toast);\n        },\n        className: cn(classNames == null ? void 0 : classNames.closeButton, toast == null ? void 0 : (_toast_classNames2 = toast.classNames) == null ? void 0 : _toast_classNames2.closeButton)\n    }, (_icons_close = icons == null ? void 0 : icons.close) != null ? _icons_close : CloseIcon) : null, (toastType || toast.icon || toast.promise) && toast.icon !== null && ((icons == null ? void 0 : icons[toastType]) !== null || toast.icon) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-icon\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.icon, toast == null ? void 0 : (_toast_classNames3 = toast.classNames) == null ? void 0 : _toast_classNames3.icon)\n    }, toast.promise || toast.type === 'loading' && !toast.icon ? toast.icon || getLoadingIcon() : null, toast.type !== 'loading' ? icon : null) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-content\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.content, toast == null ? void 0 : (_toast_classNames4 = toast.classNames) == null ? void 0 : _toast_classNames4.content)\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-title\": \"\",\n        className: cn(classNames == null ? void 0 : classNames.title, toast == null ? void 0 : (_toast_classNames5 = toast.classNames) == null ? void 0 : _toast_classNames5.title)\n    }, toast.jsx ? toast.jsx : typeof toast.title === 'function' ? toast.title() : toast.title), toast.description ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"div\", {\n        \"data-description\": \"\",\n        className: cn(descriptionClassName, toastDescriptionClassname, classNames == null ? void 0 : classNames.description, toast == null ? void 0 : (_toast_classNames6 = toast.classNames) == null ? void 0 : _toast_classNames6.description)\n    }, typeof toast.description === 'function' ? toast.description() : toast.description) : null), /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(toast.cancel) ? toast.cancel : toast.cancel && isAction(toast.cancel) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": true,\n        \"data-cancel\": true,\n        style: toast.cancelButtonStyle || cancelButtonStyle,\n        onClick: (event)=>{\n            // We need to check twice because typescript\n            if (!isAction(toast.cancel)) return;\n            if (!dismissible) return;\n            toast.cancel.onClick == null ? void 0 : toast.cancel.onClick.call(toast.cancel, event);\n            deleteToast();\n        },\n        className: cn(classNames == null ? void 0 : classNames.cancelButton, toast == null ? void 0 : (_toast_classNames7 = toast.classNames) == null ? void 0 : _toast_classNames7.cancelButton)\n    }, toast.cancel.label) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(toast.action) ? toast.action : toast.action && isAction(toast.action) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"button\", {\n        \"data-button\": true,\n        \"data-action\": true,\n        style: toast.actionButtonStyle || actionButtonStyle,\n        onClick: (event)=>{\n            // We need to check twice because typescript\n            if (!isAction(toast.action)) return;\n            toast.action.onClick == null ? void 0 : toast.action.onClick.call(toast.action, event);\n            if (event.defaultPrevented) return;\n            deleteToast();\n        },\n        className: cn(classNames == null ? void 0 : classNames.actionButton, toast == null ? void 0 : (_toast_classNames8 = toast.classNames) == null ? void 0 : _toast_classNames8.actionButton)\n    }, toast.action.label) : null);\n};\nfunction getDocumentDirection() {\n    if (true) return 'ltr';\n    if (typeof document === 'undefined') return 'ltr'; // For Fresh purpose\n    const dirAttribute = document.documentElement.getAttribute('dir');\n    if (dirAttribute === 'auto' || !dirAttribute) {\n        return window.getComputedStyle(document.documentElement).direction;\n    }\n    return dirAttribute;\n}\nfunction assignOffset(defaultOffset, mobileOffset) {\n    const styles = {};\n    [\n        defaultOffset,\n        mobileOffset\n    ].forEach((offset, index)=>{\n        const isMobile = index === 1;\n        const prefix = isMobile ? '--mobile-offset' : '--offset';\n        const defaultValue = isMobile ? MOBILE_VIEWPORT_OFFSET : VIEWPORT_OFFSET;\n        function assignAll(offset) {\n            [\n                'top',\n                'right',\n                'bottom',\n                'left'\n            ].forEach((key)=>{\n                styles[`${prefix}-${key}`] = typeof offset === 'number' ? `${offset}px` : offset;\n            });\n        }\n        if (typeof offset === 'number' || typeof offset === 'string') {\n            assignAll(offset);\n        } else if (typeof offset === 'object') {\n            [\n                'top',\n                'right',\n                'bottom',\n                'left'\n            ].forEach((key)=>{\n                if (offset[key] === undefined) {\n                    styles[`${prefix}-${key}`] = defaultValue;\n                } else {\n                    styles[`${prefix}-${key}`] = typeof offset[key] === 'number' ? `${offset[key]}px` : offset[key];\n                }\n            });\n        } else {\n            assignAll(defaultValue);\n        }\n    });\n    return styles;\n}\nfunction useSonner() {\n    const [activeToasts, setActiveToasts] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"useSonner.useEffect\": ()=>{\n            return ToastState.subscribe({\n                \"useSonner.useEffect\": (toast)=>{\n                    if (toast.dismiss) {\n                        setTimeout({\n                            \"useSonner.useEffect\": ()=>{\n                                react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                    \"useSonner.useEffect\": ()=>{\n                                        setActiveToasts({\n                                            \"useSonner.useEffect\": (toasts)=>toasts.filter({\n                                                    \"useSonner.useEffect\": (t)=>t.id !== toast.id\n                                                }[\"useSonner.useEffect\"])\n                                        }[\"useSonner.useEffect\"]);\n                                    }\n                                }[\"useSonner.useEffect\"]);\n                            }\n                        }[\"useSonner.useEffect\"]);\n                        return;\n                    }\n                    // Prevent batching, temp solution.\n                    setTimeout({\n                        \"useSonner.useEffect\": ()=>{\n                            react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                \"useSonner.useEffect\": ()=>{\n                                    setActiveToasts({\n                                        \"useSonner.useEffect\": (toasts)=>{\n                                            const indexOfExistingToast = toasts.findIndex({\n                                                \"useSonner.useEffect.indexOfExistingToast\": (t)=>t.id === toast.id\n                                            }[\"useSonner.useEffect.indexOfExistingToast\"]);\n                                            // Update the toast if it already exists\n                                            if (indexOfExistingToast !== -1) {\n                                                return [\n                                                    ...toasts.slice(0, indexOfExistingToast),\n                                                    {\n                                                        ...toasts[indexOfExistingToast],\n                                                        ...toast\n                                                    },\n                                                    ...toasts.slice(indexOfExistingToast + 1)\n                                                ];\n                                            }\n                                            return [\n                                                toast,\n                                                ...toasts\n                                            ];\n                                        }\n                                    }[\"useSonner.useEffect\"]);\n                                }\n                            }[\"useSonner.useEffect\"]);\n                        }\n                    }[\"useSonner.useEffect\"]);\n                }\n            }[\"useSonner.useEffect\"]);\n        }\n    }[\"useSonner.useEffect\"], []);\n    return {\n        toasts: activeToasts\n    };\n}\nconst Toaster = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(function Toaster(props, ref) {\n    const { invert, position = 'bottom-right', hotkey = [\n        'altKey',\n        'KeyT'\n    ], expand, closeButton, className, offset, mobileOffset, theme = 'light', richColors, duration, style, visibleToasts = VISIBLE_TOASTS_AMOUNT, toastOptions, dir = getDocumentDirection(), gap = GAP, icons, containerAriaLabel = 'Notifications' } = props;\n    const [toasts, setToasts] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    const possiblePositions = react__WEBPACK_IMPORTED_MODULE_0__.useMemo({\n        \"Toaster.Toaster.useMemo[possiblePositions]\": ()=>{\n            return Array.from(new Set([\n                position\n            ].concat(toasts.filter({\n                \"Toaster.Toaster.useMemo[possiblePositions]\": (toast)=>toast.position\n            }[\"Toaster.Toaster.useMemo[possiblePositions]\"]).map({\n                \"Toaster.Toaster.useMemo[possiblePositions]\": (toast)=>toast.position\n            }[\"Toaster.Toaster.useMemo[possiblePositions]\"]))));\n        }\n    }[\"Toaster.Toaster.useMemo[possiblePositions]\"], [\n        toasts,\n        position\n    ]);\n    const [heights, setHeights] = react__WEBPACK_IMPORTED_MODULE_0__.useState([]);\n    const [expanded, setExpanded] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [interacting, setInteracting] = react__WEBPACK_IMPORTED_MODULE_0__.useState(false);\n    const [actualTheme, setActualTheme] = react__WEBPACK_IMPORTED_MODULE_0__.useState(theme !== 'system' ? theme :  false ? 0 : 'light');\n    const listRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const hotkeyLabel = hotkey.join('+').replace(/Key/g, '').replace(/Digit/g, '');\n    const lastFocusedElementRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(null);\n    const isFocusWithinRef = react__WEBPACK_IMPORTED_MODULE_0__.useRef(false);\n    const removeToast = react__WEBPACK_IMPORTED_MODULE_0__.useCallback({\n        \"Toaster.Toaster.useCallback[removeToast]\": (toastToRemove)=>{\n            setToasts({\n                \"Toaster.Toaster.useCallback[removeToast]\": (toasts)=>{\n                    var _toasts_find;\n                    if (!((_toasts_find = toasts.find({\n                        \"Toaster.Toaster.useCallback[removeToast]\": (toast)=>toast.id === toastToRemove.id\n                    }[\"Toaster.Toaster.useCallback[removeToast]\"])) == null ? void 0 : _toasts_find.delete)) {\n                        ToastState.dismiss(toastToRemove.id);\n                    }\n                    return toasts.filter({\n                        \"Toaster.Toaster.useCallback[removeToast]\": ({ id })=>id !== toastToRemove.id\n                    }[\"Toaster.Toaster.useCallback[removeToast]\"]);\n                }\n            }[\"Toaster.Toaster.useCallback[removeToast]\"]);\n        }\n    }[\"Toaster.Toaster.useCallback[removeToast]\"], []);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            return ToastState.subscribe({\n                \"Toaster.Toaster.useEffect\": (toast)=>{\n                    if (toast.dismiss) {\n                        // Prevent batching of other state updates\n                        requestAnimationFrame({\n                            \"Toaster.Toaster.useEffect\": ()=>{\n                                setToasts({\n                                    \"Toaster.Toaster.useEffect\": (toasts)=>toasts.map({\n                                            \"Toaster.Toaster.useEffect\": (t)=>t.id === toast.id ? {\n                                                    ...t,\n                                                    delete: true\n                                                } : t\n                                        }[\"Toaster.Toaster.useEffect\"])\n                                }[\"Toaster.Toaster.useEffect\"]);\n                            }\n                        }[\"Toaster.Toaster.useEffect\"]);\n                        return;\n                    }\n                    // Prevent batching, temp solution.\n                    setTimeout({\n                        \"Toaster.Toaster.useEffect\": ()=>{\n                            react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync({\n                                \"Toaster.Toaster.useEffect\": ()=>{\n                                    setToasts({\n                                        \"Toaster.Toaster.useEffect\": (toasts)=>{\n                                            const indexOfExistingToast = toasts.findIndex({\n                                                \"Toaster.Toaster.useEffect.indexOfExistingToast\": (t)=>t.id === toast.id\n                                            }[\"Toaster.Toaster.useEffect.indexOfExistingToast\"]);\n                                            // Update the toast if it already exists\n                                            if (indexOfExistingToast !== -1) {\n                                                return [\n                                                    ...toasts.slice(0, indexOfExistingToast),\n                                                    {\n                                                        ...toasts[indexOfExistingToast],\n                                                        ...toast\n                                                    },\n                                                    ...toasts.slice(indexOfExistingToast + 1)\n                                                ];\n                                            }\n                                            return [\n                                                toast,\n                                                ...toasts\n                                            ];\n                                        }\n                                    }[\"Toaster.Toaster.useEffect\"]);\n                                }\n                            }[\"Toaster.Toaster.useEffect\"]);\n                        }\n                    }[\"Toaster.Toaster.useEffect\"]);\n                }\n            }[\"Toaster.Toaster.useEffect\"]);\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        toasts\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            if (theme !== 'system') {\n                setActualTheme(theme);\n                return;\n            }\n            if (theme === 'system') {\n                // check if current preference is dark\n                if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {\n                    // it's currently dark\n                    setActualTheme('dark');\n                } else {\n                    // it's not dark\n                    setActualTheme('light');\n                }\n            }\n            if (true) return;\n            const darkMediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n            try {\n                // Chrome & Firefox\n                darkMediaQuery.addEventListener('change', {\n                    \"Toaster.Toaster.useEffect\": ({ matches })=>{\n                        if (matches) {\n                            setActualTheme('dark');\n                        } else {\n                            setActualTheme('light');\n                        }\n                    }\n                }[\"Toaster.Toaster.useEffect\"]);\n            } catch (error) {\n                // Safari < 14\n                darkMediaQuery.addListener({\n                    \"Toaster.Toaster.useEffect\": ({ matches })=>{\n                        try {\n                            if (matches) {\n                                setActualTheme('dark');\n                            } else {\n                                setActualTheme('light');\n                            }\n                        } catch (e) {\n                            console.error(e);\n                        }\n                    }\n                }[\"Toaster.Toaster.useEffect\"]);\n            }\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        theme\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            // Ensure expanded is always false when no toasts are present / only one left\n            if (toasts.length <= 1) {\n                setExpanded(false);\n            }\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        toasts\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            const handleKeyDown = {\n                \"Toaster.Toaster.useEffect.handleKeyDown\": (event)=>{\n                    var _listRef_current;\n                    const isHotkeyPressed = hotkey.every({\n                        \"Toaster.Toaster.useEffect.handleKeyDown.isHotkeyPressed\": (key)=>event[key] || event.code === key\n                    }[\"Toaster.Toaster.useEffect.handleKeyDown.isHotkeyPressed\"]);\n                    if (isHotkeyPressed) {\n                        var _listRef_current1;\n                        setExpanded(true);\n                        (_listRef_current1 = listRef.current) == null ? void 0 : _listRef_current1.focus();\n                    }\n                    if (event.code === 'Escape' && (document.activeElement === listRef.current || ((_listRef_current = listRef.current) == null ? void 0 : _listRef_current.contains(document.activeElement)))) {\n                        setExpanded(false);\n                    }\n                }\n            }[\"Toaster.Toaster.useEffect.handleKeyDown\"];\n            document.addEventListener('keydown', handleKeyDown);\n            return ({\n                \"Toaster.Toaster.useEffect\": ()=>document.removeEventListener('keydown', handleKeyDown)\n            })[\"Toaster.Toaster.useEffect\"];\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        hotkey\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect({\n        \"Toaster.Toaster.useEffect\": ()=>{\n            if (listRef.current) {\n                return ({\n                    \"Toaster.Toaster.useEffect\": ()=>{\n                        if (lastFocusedElementRef.current) {\n                            lastFocusedElementRef.current.focus({\n                                preventScroll: true\n                            });\n                            lastFocusedElementRef.current = null;\n                            isFocusWithinRef.current = false;\n                        }\n                    }\n                })[\"Toaster.Toaster.useEffect\"];\n            }\n        }\n    }[\"Toaster.Toaster.useEffect\"], [\n        listRef.current\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"section\", {\n        ref: ref,\n        \"aria-label\": `${containerAriaLabel} ${hotkeyLabel}`,\n        tabIndex: -1,\n        \"aria-live\": \"polite\",\n        \"aria-relevant\": \"additions text\",\n        \"aria-atomic\": \"false\",\n        suppressHydrationWarning: true\n    }, possiblePositions.map((position, index)=>{\n        var _heights_;\n        const [y, x] = position.split('-');\n        if (!toasts.length) return null;\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"ol\", {\n            key: position,\n            dir: dir === 'auto' ? getDocumentDirection() : dir,\n            tabIndex: -1,\n            ref: listRef,\n            className: className,\n            \"data-sonner-toaster\": true,\n            \"data-sonner-theme\": actualTheme,\n            \"data-y-position\": y,\n            \"data-x-position\": x,\n            style: {\n                '--front-toast-height': `${((_heights_ = heights[0]) == null ? void 0 : _heights_.height) || 0}px`,\n                '--width': `${TOAST_WIDTH}px`,\n                '--gap': `${gap}px`,\n                ...style,\n                ...assignOffset(offset, mobileOffset)\n            },\n            onBlur: (event)=>{\n                if (isFocusWithinRef.current && !event.currentTarget.contains(event.relatedTarget)) {\n                    isFocusWithinRef.current = false;\n                    if (lastFocusedElementRef.current) {\n                        lastFocusedElementRef.current.focus({\n                            preventScroll: true\n                        });\n                        lastFocusedElementRef.current = null;\n                    }\n                }\n            },\n            onFocus: (event)=>{\n                const isNotDismissible = event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n                if (isNotDismissible) return;\n                if (!isFocusWithinRef.current) {\n                    isFocusWithinRef.current = true;\n                    lastFocusedElementRef.current = event.relatedTarget;\n                }\n            },\n            onMouseEnter: ()=>setExpanded(true),\n            onMouseMove: ()=>setExpanded(true),\n            onMouseLeave: ()=>{\n                // Avoid setting expanded to false when interacting with a toast, e.g. swiping\n                if (!interacting) {\n                    setExpanded(false);\n                }\n            },\n            onDragEnd: ()=>setExpanded(false),\n            onPointerDown: (event)=>{\n                const isNotDismissible = event.target instanceof HTMLElement && event.target.dataset.dismissible === 'false';\n                if (isNotDismissible) return;\n                setInteracting(true);\n            },\n            onPointerUp: ()=>setInteracting(false)\n        }, toasts.filter((toast)=>!toast.position && index === 0 || toast.position === position).map((toast, index)=>{\n            var _toastOptions_duration, _toastOptions_closeButton;\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(Toast, {\n                key: toast.id,\n                icons: icons,\n                index: index,\n                toast: toast,\n                defaultRichColors: richColors,\n                duration: (_toastOptions_duration = toastOptions == null ? void 0 : toastOptions.duration) != null ? _toastOptions_duration : duration,\n                className: toastOptions == null ? void 0 : toastOptions.className,\n                descriptionClassName: toastOptions == null ? void 0 : toastOptions.descriptionClassName,\n                invert: invert,\n                visibleToasts: visibleToasts,\n                closeButton: (_toastOptions_closeButton = toastOptions == null ? void 0 : toastOptions.closeButton) != null ? _toastOptions_closeButton : closeButton,\n                interacting: interacting,\n                position: position,\n                style: toastOptions == null ? void 0 : toastOptions.style,\n                unstyled: toastOptions == null ? void 0 : toastOptions.unstyled,\n                classNames: toastOptions == null ? void 0 : toastOptions.classNames,\n                cancelButtonStyle: toastOptions == null ? void 0 : toastOptions.cancelButtonStyle,\n                actionButtonStyle: toastOptions == null ? void 0 : toastOptions.actionButtonStyle,\n                closeButtonAriaLabel: toastOptions == null ? void 0 : toastOptions.closeButtonAriaLabel,\n                removeToast: removeToast,\n                toasts: toasts.filter((t)=>t.position == toast.position),\n                heights: heights.filter((h)=>h.position == toast.position),\n                setHeights: setHeights,\n                expandByDefault: expand,\n                gap: gap,\n                expanded: expanded,\n                swipeDirections: props.swipeDirections\n            });\n        }));\n    }));\n});\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/.pnpm/sonner@2.0.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs\n");

/***/ })

};
;