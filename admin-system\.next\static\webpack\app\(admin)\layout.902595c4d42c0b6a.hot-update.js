"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(admin)/layout",{

/***/ "(app-pages-browser)/./src/components/app-sidebar.tsx":
/*!****************************************!*\
  !*** ./src/components/app-sidebar.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppSidebar: () => (/* binding */ AppSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_BarChart3_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,HelpCircle,Home,LogOut,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,HelpCircle,Home,LogOut,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,HelpCircle,Home,LogOut,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/chart-column.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,HelpCircle,Home,LogOut,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,HelpCircle,Home,LogOut,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-question-mark.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart3_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart3,HelpCircle,Home,LogOut,Settings,Users!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/avatar */ \"(app-pages-browser)/./src/components/ui/avatar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/contexts/auth-context */ \"(app-pages-browser)/./src/contexts/auth-context.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./src/lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ AppSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n// 菜单数据\nconst data = {\n    navMain: [\n        {\n            title: \"仪表板\",\n            url: \"/dashboard\",\n            icon: _barrel_optimize_names_BarChart3_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n        },\n        {\n            title: \"用户管理\",\n            url: \"/users\",\n            icon: _barrel_optimize_names_BarChart3_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n        },\n        {\n            title: \"数据分析\",\n            url: \"/analytics\",\n            icon: _barrel_optimize_names_BarChart3_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n        },\n        {\n            title: \"系统设置\",\n            url: \"/settings\",\n            icon: _barrel_optimize_names_BarChart3_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"]\n        }\n    ],\n    navSecondary: [\n        {\n            title: \"帮助中心\",\n            url: \"/help\",\n            icon: _barrel_optimize_names_BarChart3_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"]\n        }\n    ]\n};\nfunction AppSidebar(param) {\n    let { isCollapsed, onToggle } = param;\n    var _user_username;\n    _s();\n    const { user, logout } = (0,_contexts_auth_context__WEBPACK_IMPORTED_MODULE_5__.useAuth)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_6__.usePathname)();\n    const handleLogout = async ()=>{\n        await logout();\n        router.push(\"/login\");\n    };\n    const isActive = (url)=>pathname === url;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"flex h-full flex-col border-r bg-background transition-all duration-300\", isCollapsed ? \"w-16\" : \"w-64\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex h-16 items-center border-b px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 101,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this),\n                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-semibold\",\n                                    children: \"管理系统\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-muted-foreground\",\n                                    children: \"Admin Dashboard\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                            lineNumber: 104,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-auto py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-2 px-3\",\n                        children: [\n                            !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"px-3 py-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"mb-2 text-xs font-semibold text-muted-foreground uppercase tracking-wide\",\n                                    children: \"主要功能\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this),\n                            data.navMain.map((item)=>{\n                                const itemIsActive = isActive(item.url);\n                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: itemIsActive ? \"secondary\" : \"ghost\",\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"w-full justify-start h-10\", isCollapsed ? \"px-2\" : \"px-3\", itemIsActive && \"bg-secondary\"),\n                                    onClick: ()=>router.push(item.url),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                            className: \"h-4 w-4 shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 17\n                                        }, this),\n                                        !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-2 flex-1 text-left\",\n                                            children: item.title\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, item.title, true, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 15\n                                }, this);\n                            })\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 space-y-2 px-3\",\n                        children: data.navSecondary.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"w-full justify-start h-10\", isCollapsed ? \"px-2\" : \"px-3\"),\n                                onClick: ()=>router.push(item.url),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                        className: \"h-4 w-4 shrink-0\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 15\n                                    }, this),\n                                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-2\",\n                                        children: item.title\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 32\n                                    }, this)\n                                ]\n                            }, item.title, true, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 113,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border-t p-3\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenu, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuTrigger, {\n                            asChild: true,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: \"ghost\",\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_7__.cn)(\"w-full justify-start h-12\", isCollapsed ? \"px-2\" : \"px-3\"),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.Avatar, {\n                                        className: \"h-8 w-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarImage, {\n                                                src: user === null || user === void 0 ? void 0 : user.avatar_url,\n                                                alt: user === null || user === void 0 ? void 0 : user.username\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 177,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_avatar__WEBPACK_IMPORTED_MODULE_2__.AvatarFallback, {\n                                                children: (user === null || user === void 0 ? void 0 : (_user_username = user.username) === null || _user_username === void 0 ? void 0 : _user_username.charAt(0).toUpperCase()) || 'U'\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 15\n                                    }, this),\n                                    !isCollapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"ml-2 flex flex-1 flex-col items-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: (user === null || user === void 0 ? void 0 : user.full_name) || (user === null || user === void 0 ? void 0 : user.username)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: user === null || user === void 0 ? void 0 : user.email\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 185,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                lineNumber: 169,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                            lineNumber: 168,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuContent, {\n                            align: \"end\",\n                            className: \"w-56\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuLabel, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col space-y-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm font-medium\",\n                                                children: (user === null || user === void 0 ? void 0 : user.full_name) || (user === null || user === void 0 ? void 0 : user.username)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: user === null || user === void 0 ? void 0 : user.email\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                                lineNumber: 194,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuSeparator, {}, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 197,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                    onClick: ()=>router.push('/profile'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"个人设置\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                    onClick: ()=>router.push('/help'),\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"帮助中心\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 202,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuSeparator, {}, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 206,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                    onClick: handleLogout,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart3_HelpCircle_Home_LogOut_Settings_Users_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"mr-2 h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 15\n                                        }, this),\n                                        \"退出登录\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                            lineNumber: 190,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                    lineNumber: 167,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\app-sidebar.tsx\",\n        lineNumber: 93,\n        columnNumber: 5\n    }, this);\n}\n_s(AppSidebar, \"TVCmUMOhI9c7KC/d7gLgXSNlw8w=\", false, function() {\n    return [\n        _contexts_auth_context__WEBPACK_IMPORTED_MODULE_5__.useAuth,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_6__.usePathname\n    ];\n});\n_c = AppSidebar;\nvar _c;\n$RefreshReg$(_c, \"AppSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/app-sidebar.tsx\n"));

/***/ })

});