"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/(admin)/activation-codes/page",{

/***/ "(app-pages-browser)/./src/components/activation-codes/activation-code-table.tsx":
/*!*******************************************************************!*\
  !*** ./src/components/activation-codes/activation-code-table.tsx ***!
  \*******************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ActivationCodeTable: () => (/* binding */ ActivationCodeTable)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@15.4.4_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_table__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/table */ \"(app-pages-browser)/./src/components/ui/table.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_Ban_CheckCircle_Clock_Copy_Edit_Eye_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Ban,CheckCircle,Clock,Copy,Edit,Eye,MoreHorizontal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_Ban_CheckCircle_Clock_Copy_Edit_Eye_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Ban,CheckCircle,Clock,Copy,Edit,Eye,MoreHorizontal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/circle-x.js\");\n/* harmony import */ var _barrel_optimize_names_Ban_CheckCircle_Clock_Copy_Edit_Eye_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Ban,CheckCircle,Clock,Copy,Edit,Eye,MoreHorizontal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Ban_CheckCircle_Clock_Copy_Edit_Eye_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Ban,CheckCircle,Clock,Copy,Edit,Eye,MoreHorizontal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/ban.js\");\n/* harmony import */ var _barrel_optimize_names_Ban_CheckCircle_Clock_Copy_Edit_Eye_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Ban,CheckCircle,Clock,Copy,Edit,Eye,MoreHorizontal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _barrel_optimize_names_Ban_CheckCircle_Clock_Copy_Edit_Eye_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Ban,CheckCircle,Clock,Copy,Edit,Eye,MoreHorizontal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/ellipsis.js\");\n/* harmony import */ var _barrel_optimize_names_Ban_CheckCircle_Clock_Copy_Edit_Eye_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Ban,CheckCircle,Clock,Copy,Edit,Eye,MoreHorizontal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Ban_CheckCircle_Clock_Copy_Edit_Eye_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Ban,CheckCircle,Clock,Copy,Edit,Eye,MoreHorizontal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Ban_CheckCircle_Clock_Copy_Edit_Eye_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Ban,CheckCircle,Clock,Copy,Edit,Eye,MoreHorizontal,Trash2,XCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/.pnpm/lucide-react@0.525.0_react@19.1.0/node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/.pnpm/sonner@2.0.6_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _services_activation_code_service__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/services/activation-code.service */ \"(app-pages-browser)/./src/services/activation-code.service.ts\");\n/* harmony import */ var _barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=formatDistanceToNow!=!date-fns */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/formatDistanceToNow.js\");\n/* harmony import */ var date_fns_locale__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! date-fns/locale */ \"(app-pages-browser)/./node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/zh-CN.js\");\n/* __next_internal_client_entry_do_not_use__ ActivationCodeTable auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction ActivationCodeTable(param) {\n    let { codes, loading, onEdit, onDelete, onView, onStatusChange } = param;\n    _s();\n    const [copiedCode, setCopiedCode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleCopyCode = async (code)=>{\n        try {\n            await navigator.clipboard.writeText(code);\n            setCopiedCode(code);\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.success('激活码已复制到剪贴板');\n            setTimeout(()=>setCopiedCode(null), 2000);\n        } catch (error) {\n            sonner__WEBPACK_IMPORTED_MODULE_6__.toast.error('复制失败');\n        }\n    };\n    const getStatusBadge = (status)=>{\n        const statusConfig = {\n            active: {\n                label: '有效',\n                variant: 'default',\n                icon: _barrel_optimize_names_Ban_CheckCircle_Clock_Copy_Edit_Eye_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                color: 'text-green-600'\n            },\n            used: {\n                label: '已使用',\n                variant: 'secondary',\n                icon: _barrel_optimize_names_Ban_CheckCircle_Clock_Copy_Edit_Eye_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                color: 'text-gray-600'\n            },\n            expired: {\n                label: '已过期',\n                variant: 'destructive',\n                icon: _barrel_optimize_names_Ban_CheckCircle_Clock_Copy_Edit_Eye_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                color: 'text-red-600'\n            },\n            disabled: {\n                label: '已禁用',\n                variant: 'outline',\n                icon: _barrel_optimize_names_Ban_CheckCircle_Clock_Copy_Edit_Eye_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                color: 'text-orange-600'\n            }\n        };\n        const config = statusConfig[status] || statusConfig.active;\n        const Icon = config.icon;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n            variant: config.variant,\n            className: \"gap-1\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                    className: \"h-3 w-3 \".concat(config.color)\n                }, void 0, false, {\n                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 9\n                }, this),\n                config.label\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n            lineNumber: 100,\n            columnNumber: 7\n        }, this);\n    };\n    const getVipLevelBadge = (level)=>{\n        const vipConfig = _services_activation_code_service__WEBPACK_IMPORTED_MODULE_7__.ActivationCodeService.getVipLevelConfig();\n        const config = vipConfig[level];\n        if (!config) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n                variant: \"outline\",\n                children: level\n            }, void 0, false, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                lineNumber: 112,\n                columnNumber: 14\n            }, this);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_4__.Badge, {\n            variant: \"outline\",\n            className: \"gap-1\",\n            style: {\n                borderColor: config.color,\n                color: config.color\n            },\n            children: config.label\n        }, void 0, false, {\n            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, this);\n    };\n    const formatDate = (dateString)=>{\n        const date = new Date(dateString);\n        return (0,_barrel_optimize_names_formatDistanceToNow_date_fns__WEBPACK_IMPORTED_MODULE_12__.formatDistanceToNow)(date, {\n            addSuffix: true,\n            locale: date_fns_locale__WEBPACK_IMPORTED_MODULE_13__.zhCN\n        });\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"rounded-md border\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.Table, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableRow, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHead, {\n                                    children: \"激活码\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                    lineNumber: 143,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHead, {\n                                    children: \"VIP等级\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHead, {\n                                    children: \"状态\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                    lineNumber: 145,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHead, {\n                                    children: \"有效期\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHead, {\n                                    children: \"创建时间\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                    lineNumber: 147,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHead, {\n                                    children: \"使用情况\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHead, {\n                                    className: \"w-[100px]\",\n                                    children: \"操作\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                            lineNumber: 142,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                        lineNumber: 141,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableBody, {\n                        children: Array.from({\n                            length: 5\n                        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableRow, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-200 rounded animate-pulse w-32\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-6 bg-gray-200 rounded animate-pulse w-16\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                            lineNumber: 159,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                        lineNumber: 158,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-6 bg-gray-200 rounded animate-pulse w-16\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                            lineNumber: 162,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-200 rounded animate-pulse w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                            lineNumber: 165,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-200 rounded animate-pulse w-24\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                        lineNumber: 167,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-4 bg-gray-200 rounded animate-pulse w-20\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                        lineNumber: 170,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"h-8 bg-gray-200 rounded animate-pulse w-8\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                            lineNumber: 174,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                        lineNumber: 173,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, index, true, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 15\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                lineNumber: 140,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n            lineNumber: 139,\n            columnNumber: 7\n        }, this);\n    }\n    if (!codes || codes.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"rounded-md border\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.Table, {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableRow, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHead, {\n                                    children: \"激活码\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                    lineNumber: 190,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHead, {\n                                    children: \"VIP等级\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                    lineNumber: 191,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHead, {\n                                    children: \"状态\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                    lineNumber: 192,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHead, {\n                                    children: \"有效期\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                    lineNumber: 193,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHead, {\n                                    children: \"创建时间\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHead, {\n                                    children: \"使用情况\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHead, {\n                                    className: \"w-[100px]\",\n                                    children: \"操作\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                        lineNumber: 188,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableBody, {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableRow, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                colSpan: 7,\n                                className: \"h-24 text-center text-muted-foreground\",\n                                children: \"暂无激活码数据\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                lineNumber: 187,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n            lineNumber: 186,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"rounded-md border\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.Table, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHeader, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableRow, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHead, {\n                                children: \"激活码\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                lineNumber: 216,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHead, {\n                                children: \"VIP等级\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHead, {\n                                children: \"状态\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHead, {\n                                children: \"有效期\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                lineNumber: 219,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHead, {\n                                children: \"创建时间\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                lineNumber: 220,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHead, {\n                                children: \"使用情况\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                lineNumber: 221,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableHead, {\n                                className: \"w-[100px]\",\n                                children: \"操作\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                lineNumber: 222,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                        lineNumber: 215,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableBody, {\n                    children: codes.map((code)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableRow, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                className: \"text-sm font-mono bg-muted px-2 py-1 rounded\",\n                                                children: _services_activation_code_service__WEBPACK_IMPORTED_MODULE_7__.ActivationCodeService.formatCode(code.code)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"ghost\",\n                                                size: \"sm\",\n                                                onClick: ()=>handleCopyCode(code.code),\n                                                className: \"h-6 w-6 p-0\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Ban_CheckCircle_Clock_Copy_Edit_Eye_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                    className: \"h-3 w-3 \".concat(copiedCode === code.code ? 'text-green-600' : '')\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                                    lineNumber: 239,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    children: getVipLevelBadge(code.vip_level)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                    lineNumber: 243,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    children: getStatusBadge(code.status)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                    lineNumber: 244,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    className: \"text-sm\",\n                                    children: [\n                                        code.vip_duration_days,\n                                        \" 天\",\n                                        code.expires_at && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                \"过期: \",\n                                                formatDate(code.expires_at)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                            lineNumber: 248,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                    lineNumber: 245,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    className: \"text-sm\",\n                                    children: formatDate(code.created_at)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                    lineNumber: 253,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    className: \"text-sm\",\n                                    children: code.status === 'used' && code.used_at ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-green-600\",\n                                                children: \"已使用\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-muted-foreground\",\n                                                children: formatDate(code.used_at)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 21\n                                            }, this),\n                                            code.user && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-xs text-blue-600 mt-1\",\n                                                children: [\n                                                    \"用户: \",\n                                                    code.user.username,\n                                                    code.user.full_name && \" (\".concat(code.user.full_name, \")\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 19\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-muted-foreground\",\n                                        children: \"未使用\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                    lineNumber: 256,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_table__WEBPACK_IMPORTED_MODULE_2__.TableCell, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenu, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    className: \"h-8 w-8 p-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Ban_CheckCircle_Clock_Copy_Edit_Eye_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                                        lineNumber: 278,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                                    lineNumber: 277,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuContent, {\n                                                align: \"end\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuLabel, {\n                                                        children: \"操作\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                                        lineNumber: 282,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                        onClick: ()=>onView(code),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Ban_CheckCircle_Clock_Copy_Edit_Eye_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"查看详情\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                                        lineNumber: 283,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                        onClick: ()=>handleCopyCode(code.code),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Ban_CheckCircle_Clock_Copy_Edit_Eye_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                                                lineNumber: 288,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"复制激活码\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                                        lineNumber: 287,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuSeparator, {}, void 0, false, {\n                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                                        lineNumber: 291,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    code.status !== 'used' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                                onClick: ()=>onEdit(code),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Ban_CheckCircle_Clock_Copy_Edit_Eye_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                                                        lineNumber: 295,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"编辑\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                                                lineNumber: 294,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            code.status === 'active' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                                onClick: ()=>onStatusChange(code, 'disabled'),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Ban_CheckCircle_Clock_Copy_Edit_Eye_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                                                        lineNumber: 300,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"禁用\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 27\n                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                                onClick: ()=>onStatusChange(code, 'active'),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Ban_CheckCircle_Clock_Copy_Edit_Eye_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                                                        lineNumber: 305,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    \"启用\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                                                lineNumber: 309,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                                onClick: ()=>onDelete(code),\n                                                                className: \"text-red-600\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Ban_CheckCircle_Clock_Copy_Edit_Eye_MoreHorizontal_Trash2_XCircle_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                                                        lineNumber: 314,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"删除\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                                                lineNumber: 310,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                                lineNumber: 281,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                        lineNumber: 275,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                                    lineNumber: 274,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, code.id, true, {\n                            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n            lineNumber: 213,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\7mouthMission\\\\uniFigma\\\\admin-system\\\\src\\\\components\\\\activation-codes\\\\activation-code-table.tsx\",\n        lineNumber: 212,\n        columnNumber: 5\n    }, this);\n}\n_s(ActivationCodeTable, \"+rbbokKfOObQlaF8o0GLfbqs0e4=\");\n_c = ActivationCodeTable;\nvar _c;\n$RefreshReg$(_c, \"ActivationCodeTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/activation-codes/activation-code-table.tsx\n"));

/***/ })

});